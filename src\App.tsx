import React, { useState } from 'react';
import './App.css'
import logo from './assets/images/logo.png';
import ebiLogo from './assets/images/logo (1).png';
import {
  TextField,
  Box,
  Typography,
  Button,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  Select,
  MenuItem,
  InputLabel,
  Checkbox,
  FormGroup,
  Paper,
  Container,
  Divider,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  AppBar,
  Toolbar,
  ThemeProvider,
  useTheme,
  useMediaQuery
} from "@mui/material";
import { createTheme } from '@mui/material/styles';
import { ArrowBack, ArrowForward } from '@mui/icons-material';

interface FormData {
  // General Information
  companyName: string;

  // Energy Consumption
  energyConsumptionType: string;
  renewableEnergyElements: string[];
  nonRenewableEnergyElements: string[];
  windElectricity: string;
  solarElectricity: string;
  otherRenewableElectricity: string;
  gridElectricityConsumed: string;
  fuelConsumed: string;
  otherEnergyConsumed: string;
  totalEnergyConsumed: string;
  gridEnergyPercentage: string;
  renewablePercentage: string;
  numberOfEmployees: string;
  energyPerEmployee: string;

  // Water Consumption
  waterSource: string[];
  totalWaterWithdrawn: string;
  totalWaterConsumed: string;
  waterReused: string;
  highWaterStressRegions: string;
  waterWithdrawnStress: string;
  waterConsumedStress: string;

  // Emissions
  emissionSources: string[];
  dieselUnits: string;
  dieselCO2: string;
  petrolUnits: string;
  petrolCO2: string;
  keroseneUnits: string;
  keroseneCO2: string;
  electricityUnits: string;
  electricityCO2: string;

  // Waste Management
  wasteActions: string[];
  ewasteQuantity: string;
  ewasteHazardous: string;
  paperWasteQuantity: string;
  paperWasteHazardous: string;
  foodWasteQuantity: string;
  foodWasteHazardous: string;
  paperRecycledQuantity: string;
  paperRecycledPercentage: string;
  plasticRecycledQuantity: string;
  plasticRecycledPercentage: string;
  organicWasteQuantity: string;
  landfillWaste: string;
  landfillQuantity: string;

  // GHG Emissions
  naturalGas: string;
  distillateFuelOil: string;
  gasoline: string;
  refrigerants: string;
  purchasedElectricity: string;
  heatingCooling: string;
  travel: string;
  purchasedGoods: string;
  upstreamTransportation: string;
  wasteFromOperations: string;

  // Compliance
  ehsPractices: string;
  nonCompliance: string;
  finesPenalties: string;
  nonComplianceDetails: string;
  codeOfConduct: string;
  environmentalRegulations: string;

  // Assessments
  supplierRiskAssessment: string;
  supplierAssessmentFrequency: string;
  supplierAudits: string;
  correctiveActionPlans: string;
  carbonReductionTargets: string;

  // Financial Contributions
  financialContributions: string;
  directFinancialValue: string;
  indirectFinancialValue: string;
  inKindContributions: string;
  directInKindValue: string;
  indirectInKindValue: string;
  inKindEstimation: string;
  sbtiTargets: string;
  sbtiDescription: string;

  // Gender Representation
  femaleGenderLeadershipRange: string;
  maleGenderLeadershipRange: string;
  femaleGenderManagementRange: string;
  maleGenderManagementRange: string;
  femaleGenderTechnicalRange: string;
  maleGenderTechnicalRange: string;

  // Age Group Representation
  femaleAgeLeadershipRange: string;
  maleAgeLeadershipRange: string;
  femaleAgeManagementRange: string;
  maleAgeManagementRange: string;
  femaleAgeTechnicalRange: string;
  maleAgeTechnicalRange: string;

  // Age Group Representation
  femaleDisabilityLeadershipRange: string;
  maleDisabilityLeadershipRange: string;
  femaleDisabilityManagementRange: string;
  maleDisabilityManagementRange: string;
  femaleDisabilityTechnicalRange: string;
  maleDisabilityTechnicalRange: string;

  // Ethnicity Female Representation
  sinhalaFemaleRange: string;
  tamilFemaleRange: string;
  moorsFemaleRange: string;
  burgherFemaleRange: string;
  otherFemaleRange: string;

  // Ethnicity Male Representation
  sinhalaMaleRange: string;
  tamilMaleRange: string;
  moorsMaleRange: string;
  burgherMaleRange: string;
  otherMaleRange: string;

  // individuals within the organization’s governance
  femaleIndividualGenderRange: string;
  maleIndividualGenderRange: string;
  femaleIndividualAgeRange: string;
  maleIndividualAgeRange: string;

  // employee turnover
  femaleEmployeeTurnoverGenderRange: string;
  maleEmployeeTurnoverGenderRange: string;
  femaleEmployeeTurnoverAgeRange: string;
  maleEmployeeTurnoverAgeRange: string;
  leadershipEmployeeTurnoverCategoryRange: string;
  managementEmployeeTurnoverCategoryRange: string;
  technicalEmployeeTurnoverCategoryRange: string;
  otherEmployeeTurnoverCategoryRange: string;

  // new employee hires
  femaleNewEmployeeHiresGenderRange: string;
  maleNewEmployeeHiresGenderRange: string;
  femaleNewEmployeeHiresAgeRange: string;
  maleNewEmployeeHiresAgeRange: string;
  leadershipNewEmployeeHiresCategoryRange: string;
  managementNewEmployeeHiresCategoryRange: string;
  technicalNewEmployeeHiresCategoryRange: string;
  otherNewEmployeeHiresCategoryRange: string;
  nonOrganizationControlledWorkers: string;

  // labor relations
  haveHRPolicy: string;
  hrPoliciesInclude: string;
  policiesCommunicated: string;

  // Child forced or compulsory labour
  ageVerificationMechanism: string;
  childLabourRiskPolicy: string;

  // Health and Well-being
  haveHealthAndSafetyPolicy: string;
  healthPoliciesInclude: string;

  // skills development
  haveTrainingPolicies: string;
  describeTrainingPolicy: string;
  haveSkillDevelopmentTraining: string;
  femaleTrainingHoursGenderRange: string;
  maleTrainingHoursGenderRange: string;
  leadershipTrainingHoursCategoryRange: string;
  managementTrainingHoursCategoryRange: string;
  technicalTrainingHoursCategoryRange: string;
  otherTrainingHoursCategoryRange: string;

  // products and services
  haveTechForGoodPolicy: string;
  describeTechForGoodPolicy: string;
  researchAndDevelopmentCost: string;

  // Community and Social vitality
  haveDataSecurityPolicy: string;
  describeDataSecurityPolicy: string;
  substantiatedComplaintsReceived: string;
  identifiedLossesOfData: string;
  usersAffectedDataBreaches: string;

  // Community Engagement
  organizationEngagementCommunities: string;
  haveSupplierSocialPolicy: string;
  supplierDueDiligencePolicy: string;

  // Pay Gap
  femaleLeadershipBasicSalleryByGenderRange: string;
  femaleManagementBasicSalleryByGenderRange: string;
  femaleTechnicalBasicSalleryByGenderRange: string;
  femaleOtherBasicSalleryByGenderRange: string;
  maleLeadershipBasicSalleryByGenderRange: string;
  maleManagementBasicSalleryByGenderRange: string;
  maleTechnicalBasicSalleryByGenderRange: string;
  maleOtherBasicSalleryByGenderRange: string;
  femaleLeadershipMinimumWageByGenderRange: string;
  femaleManagementMinimumWageByGenderRange: string;
  femaleTechnicalMinimumWageByGenderRange: string;
  femaleOtherMinimumWageByGenderRange: string;
  maleLeadershipMinimumWageByGenderRange: string;
  maleManagementMinimumWageByGenderRange: string;
  maleTechnicalMinimumWageByGenderRange: string;
  maleOtherMinimumWageByGenderRange: string;

  // Parental leave
  tookParentalLeave: string;
  returnParentalLeaveCount: string;
  stillEmployedCount: string;
  rateOfTookParentalLeave: string;

  // Incidents of discrimination
  totalIncidentsOfDiscrimination: string;
  reviewedIncidentsCount: string;
  remediationPlansBeingImplemented: string;
  remediationPlansImplemented: string;
  incidentsNoLongerSubjectToAction: string;
  staffTrainingConducted: string;
  unconsciousBiasTrainingConducted: string;

  // Health and Safety
  healthAndSafetyManagementSystem: string;
  healthAndSafetyManagementSystemImplemented: string;
  healthAndSafetyManagementSystemImplementedLegalRequirements: string;
  healthAndSafetyManagementSystemImplementedRiskManagement: string;
  healthAndSafetyManagementSystemScope: string;
  healthAndSafetyManagementSystemWorkersNotCovered: string;
  healthAndSafetyManagementSystemBestPracticeProgrammes: string;
  healthAndSafetyManagementSystemWorkRelatedIllHealth: string;

  // Skill Development
  scopeOfProgrammes: string;
  specifyAssistancePrograms: string;

  // Employee reviews
  femaleEmployeeReviewsGenderRange: string;
  maleEmployeeReviewsGenderRange: string;
  femaleLeadershipEmployeeReviewsCategoryRange: string;
  femaleManagementEmployeeReviewsCategoryRange: string;
  femaleTechnicalEmployeeReviewsCategoryRange: string;
  femaleOtherEmployeeReviewsCategoryRange: string;
  maleLeadershipEmployeeReviewsCategoryRange: string;
  maleManagementEmployeeReviewsCategoryRange: string;
  maleTechnicalEmployeeReviewsCategoryRange: string;
  maleOtherEmployeeReviewsCategoryRange: string;

  // Products and services
  detailOnProduct: string;
  descriptionOfPolicies: string;
  organizationImplementIntiatives: string[];

  // Supplier due diligence
  numberSuppliersAssessed: string;
  numberSuppliersSignificantImpacts: string;
  numberSuppliersImprovementsAgreed: string;
  numberSuppliersRelationshipTerminated: string;
  significantNegativeSocialImpacts: string;

  // Certifications
  diversityAndInclusionCertifications: string;
  healthAndSafetyCertifications: string;

  // Monetized impacts of training
  increasedRevenueDueToTraining: string;
  productivityGainsDueToTraining: string;
  employeeEngagementDueToTraining: string;
  internalHireRates: string;

  // Innovation of better products and services
  benefitsOnRevenueFromServices: string;
  techForGoodRecognitions: string;

  // Local community engagement
  socialImpactAssessments: string;
  publicDisclosureOfResults: string;
  workerRepresentationBodies: string;
  localCommunityDevelopmentPrograms: string;
  stakeholderEngagementPlans: string;
  formalGrievanceProcesses: string;
}

const sections = [
  'Female and Male Representation',
  'Age Group Representation',
  'Disability Representation',
  'Ethnicity Representation',
  'Individuals within the organization’s governance',
  'Employee Turnover',
  'New Employee Hires',
  'Labor Relations',
  'Child forced or compulsory labour',
  'Health and Well-being',
  'Skill Development',
  'Innovation of better products and services',
  'Community and Social vitality',
  'Community Engagement',
  'Gender Pay Gap',
  'Parental Leave',
  'Incidents of discrimination',
  'Health and Well-being',
  'Skill Development',
  'Employee reviews',
  'Products and services',
  'Supplier due diligence',
  'Certifications',
  'Monetized impacts of training',
  'Local community engagement',
];

// Create theme with Poppins font and responsive breakpoints
const theme = createTheme({
  typography: {
    fontFamily: '"Poppins", "Roboto", "Helvetica", "Arial", sans-serif',
    h6: {
      fontSize: '1.25rem',
      '@media (max-width:600px)': {
        fontSize: '1.1rem',
      },
    },
    body1: {
      '@media (max-width:600px)': {
        fontSize: '0.875rem',
      },
    },
    body2: {
      '@media (max-width:600px)': {
        fontSize: '0.75rem',
      },
    },
  },
  components: {
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiInputLabel-root': {
            fontSize: '1rem',
            '@media (max-width:600px)': {
              fontSize: '0.875rem',
            },
          },
          '& .MuiInputBase-input': {
            fontSize: '1rem',
            '@media (max-width:600px)': {
              fontSize: '0.875rem',
            },
          },
        },
      },
    },
    MuiFormControl: {
      styleOverrides: {
        root: {
          '& .MuiInputLabel-root': {
            fontSize: '1rem',
            '@media (max-width:600px)': {
              fontSize: '0.875rem',
            },
          },
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          fontSize: '0.875rem',
          '@media (max-width:600px)': {
            fontSize: '0.8rem',
            padding: '6px 12px',
          },
        },
      },
    },
    MuiFormGroup: {
      styleOverrides: {
        root: {
          '@media (max-width:600px)': {
            '& .MuiFormControlLabel-root': {
              marginLeft: 0,
              marginRight: 0,
            },
          },
        },
      },
    },
    MuiFormControlLabel: {
      styleOverrides: {
        root: {
          '& .MuiFormControlLabel-label': {
            fontSize: '0.875rem',
            '@media (max-width:600px)': {
              fontSize: '0.8rem',
            },
          },
        },
      },
    },
  },
  breakpoints: {
    values: {
      xs: 0,
      sm: 600,
      md: 900,
      lg: 1200,
      xl: 1536,
    },
  },
});

// Responsive Radio Group Table Component
interface ResponsiveRadioTableProps {
  title: string;
  headers: string[];
  rows: {
    label: string;
    value: string;
    onChange: (event: any) => void;
    options: string[];
  }[];
}

const ResponsiveRadioTable: React.FC<ResponsiveRadioTableProps> = ({ title, headers, rows }) => (
  <Box sx={{ mb: 4 }}>
    <Typography variant="h6" gutterBottom sx={{ fontSize: { xs: '1.1rem', sm: '1.25rem' } }}>
      {title}
    </Typography>

    {/* Header Row */}
    <Box sx={{
      display: 'flex',
      alignItems: 'center',
      mb: 2,
      flexDirection: { xs: 'column', sm: 'row' },
      gap: { xs: 1, sm: 0 }
    }}>
      <Box sx={{
        width: { xs: '100%', sm: '120px' },
        textAlign: { xs: 'center', sm: 'left' }
      }}></Box>
      <Box sx={{
        display: 'flex',
        flex: 1,
        justifyContent: 'space-around',
        flexWrap: { xs: 'nowrap', sm: 'nowrap' },
        gap: { xs: 0.5, sm: 0 },
        overflow: { xs: 'hidden', sm: 'visible' }
      }}>
        {headers.map((header, index) => (
          <Typography
            key={index}
            variant="body2"
            sx={{
              fontWeight: 'bold',
              textAlign: 'center',
              minWidth: { xs: '50px', sm: '80px' },
              maxWidth: { xs: '70px', sm: 'none' },
              fontSize: { xs: '0.65rem', sm: '0.875rem' },
              lineHeight: { xs: 1.2, sm: 1.43 },
              wordBreak: 'break-word',
              flex: { xs: '1 1 0', sm: 'none' }
            }}
          >
            {header}
          </Typography>
        ))}
      </Box>
    </Box>

    {/* Data Rows */}
    {rows.map((row, rowIndex) => (
      <Box key={rowIndex} sx={{
        display: 'flex',
        alignItems: 'center',
        mb: 2,
        flexDirection: { xs: 'column', sm: 'row' },
        gap: { xs: 1, sm: 0 }
      }}>
        <Typography variant="body1" sx={{
          width: { xs: '100%', sm: '120px' },
          fontWeight: 'medium',
          textAlign: { xs: 'center', sm: 'left' },
          fontSize: { xs: '0.875rem', sm: '1rem' }
        }}>
          {row.label}
        </Typography>
        <RadioGroup
          row
          value={row.value}
          onChange={row.onChange}
          sx={{
            display: 'flex',
            flex: 1,
            justifyContent: 'space-around',
            flexWrap: { xs: 'nowrap', sm: 'nowrap' },
            gap: { xs: 0.25, sm: 0 },
            overflow: { xs: 'hidden', sm: 'visible' }
          }}
        >
          {row.options.map((option, optionIndex) => (
            <FormControlLabel
              key={optionIndex}
              value={option}
              control={<Radio sx={{
                '& .MuiSvgIcon-root': { fontSize: { xs: 16, sm: 24 } },
                padding: { xs: '4px', sm: '9px' }
              }} />}
              label=""
              sx={{
                margin: 0,
                justifyContent: 'center',
                minWidth: { xs: '50px', sm: '80px' },
                maxWidth: { xs: '70px', sm: 'none' },
                flex: { xs: '1 1 0', sm: 'none' }
              }}
            />
          ))}
        </RadioGroup>
      </Box>
    ))}
  </Box>
);

function App() {
  const muiTheme = useTheme();
  const isMobile = useMediaQuery(muiTheme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(muiTheme.breakpoints.down('md'));

  const [currentSection, setCurrentSection] = useState(0);
  const [formData, setFormData] = useState<FormData>({
    // General Information
    companyName: '',

    // Energy Consumption
    energyConsumptionType: '',
    renewableEnergyElements: [],
    nonRenewableEnergyElements: [],
    windElectricity: '',
    solarElectricity: '',
    otherRenewableElectricity: '',
    gridElectricityConsumed: '',
    fuelConsumed: '',
    otherEnergyConsumed: '',
    totalEnergyConsumed: '',
    gridEnergyPercentage: '',
    renewablePercentage: '',
    numberOfEmployees: '',
    energyPerEmployee: '',

    // Water Consumption
    waterSource: [],
    totalWaterWithdrawn: '',
    totalWaterConsumed: '',
    waterReused: '',
    highWaterStressRegions: '',
    waterWithdrawnStress: '',
    waterConsumedStress: '',

    // Emissions
    emissionSources: [],
    dieselUnits: '',
    dieselCO2: '',
    petrolUnits: '',
    petrolCO2: '',
    keroseneUnits: '',
    keroseneCO2: '',
    electricityUnits: '',
    electricityCO2: '',

    // Waste Management
    wasteActions: [],
    ewasteQuantity: '',
    ewasteHazardous: '',
    paperWasteQuantity: '',
    paperWasteHazardous: '',
    foodWasteQuantity: '',
    foodWasteHazardous: '',
    paperRecycledQuantity: '',
    paperRecycledPercentage: '',
    plasticRecycledQuantity: '',
    plasticRecycledPercentage: '',
    organicWasteQuantity: '',
    landfillWaste: '',
    landfillQuantity: '',

    // GHG Emissions
    naturalGas: '',
    distillateFuelOil: '',
    gasoline: '',
    refrigerants: '',
    purchasedElectricity: '',
    heatingCooling: '',
    travel: '',
    purchasedGoods: '',
    upstreamTransportation: '',
    wasteFromOperations: '',

    // Compliance
    ehsPractices: '',
    nonCompliance: '',
    finesPenalties: '',
    nonComplianceDetails: '',
    codeOfConduct: '',
    environmentalRegulations: '',

    // Assessments
    supplierRiskAssessment: '',
    supplierAssessmentFrequency: '',
    supplierAudits: '',
    correctiveActionPlans: '',
    carbonReductionTargets: '',

    // Financial Contributions
    financialContributions: '',
    directFinancialValue: '',
    indirectFinancialValue: '',
    inKindContributions: '',
    directInKindValue: '',
    indirectInKindValue: '',
    inKindEstimation: '',
    sbtiTargets: '',
    sbtiDescription: '',

    // Gender Representation
    femaleGenderLeadershipRange: '',
    maleGenderLeadershipRange: '',
    femaleGenderManagementRange: '',
    maleGenderManagementRange: '',
    femaleGenderTechnicalRange: '',
    maleGenderTechnicalRange: '',

    // Age Group Representation
    femaleAgeLeadershipRange: '',
    maleAgeLeadershipRange: '',
    femaleAgeManagementRange: '',
    maleAgeManagementRange: '',
    femaleAgeTechnicalRange: '',
    maleAgeTechnicalRange: '',

    // Disability Representation
    femaleDisabilityLeadershipRange: '',
    maleDisabilityLeadershipRange: '',
    femaleDisabilityManagementRange: '',
    maleDisabilityManagementRange: '',
    femaleDisabilityTechnicalRange: '',
    maleDisabilityTechnicalRange: '',

    // Ethnicity Female Representation
    sinhalaFemaleRange: '',
    tamilFemaleRange: '',
    moorsFemaleRange: '',
    burgherFemaleRange: '',
    otherFemaleRange: '',

    // Ethnicity Male Representation
    sinhalaMaleRange: '',
    tamilMaleRange: '',
    moorsMaleRange: '',
    burgherMaleRange: '',
    otherMaleRange: '',

    // individuals within the organization’s governance
    femaleIndividualGenderRange: '',
    maleIndividualGenderRange: '',
    femaleIndividualAgeRange: '',
    maleIndividualAgeRange: '',

    // employee turnover
    femaleEmployeeTurnoverGenderRange: '',
    maleEmployeeTurnoverGenderRange: '',
    femaleEmployeeTurnoverAgeRange: '',
    maleEmployeeTurnoverAgeRange: '',
    leadershipEmployeeTurnoverCategoryRange: '',
    managementEmployeeTurnoverCategoryRange: '',
    technicalEmployeeTurnoverCategoryRange: '',
    otherEmployeeTurnoverCategoryRange: '',

    // new employee hires
    femaleNewEmployeeHiresGenderRange: '',
    maleNewEmployeeHiresGenderRange: '',
    femaleNewEmployeeHiresAgeRange: '',
    maleNewEmployeeHiresAgeRange: '',
    leadershipNewEmployeeHiresCategoryRange: '',
    managementNewEmployeeHiresCategoryRange: '',
    technicalNewEmployeeHiresCategoryRange: '',
    otherNewEmployeeHiresCategoryRange: '',
    nonOrganizationControlledWorkers: '',

    // labor relations
    haveHRPolicy: '',
    hrPoliciesInclude: '',
    policiesCommunicated: '',

    // Child forced or compulsory labour
    ageVerificationMechanism: '',
    childLabourRiskPolicy: '',

    // Health and Well-being
    haveHealthAndSafetyPolicy: '',
    healthPoliciesInclude: '',

    // skill development
    haveTrainingPolicies: '',
    describeTrainingPolicy: '',
    haveSkillDevelopmentTraining: '',
    femaleTrainingHoursGenderRange: '',
    maleTrainingHoursGenderRange: '',
    leadershipTrainingHoursCategoryRange: '',
    managementTrainingHoursCategoryRange: '',
    technicalTrainingHoursCategoryRange: '',
    otherTrainingHoursCategoryRange: '',

    // products and services
    haveTechForGoodPolicy: '',
    describeTechForGoodPolicy: '',
    researchAndDevelopmentCost: '',

    // Community and Social vitality
    haveDataSecurityPolicy: '',
    describeDataSecurityPolicy: '',
    substantiatedComplaintsReceived: '',
    identifiedLossesOfData: '',
    usersAffectedDataBreaches: '',

    // Community Engagement
    organizationEngagementCommunities: '',
    haveSupplierSocialPolicy: '',
    supplierDueDiligencePolicy: '',

    // Pay Gap
    femaleLeadershipBasicSalleryByGenderRange: '',
    femaleManagementBasicSalleryByGenderRange: '',
    femaleTechnicalBasicSalleryByGenderRange: '',
    femaleOtherBasicSalleryByGenderRange: '',
    maleLeadershipBasicSalleryByGenderRange: '',
    maleManagementBasicSalleryByGenderRange: '',
    maleTechnicalBasicSalleryByGenderRange: '',
    maleOtherBasicSalleryByGenderRange: '',
    femaleLeadershipMinimumWageByGenderRange: '',
    femaleManagementMinimumWageByGenderRange: '',
    femaleTechnicalMinimumWageByGenderRange: '',
    femaleOtherMinimumWageByGenderRange: '',
    maleLeadershipMinimumWageByGenderRange: '',
    maleManagementMinimumWageByGenderRange: '',
    maleTechnicalMinimumWageByGenderRange: '',
    maleOtherMinimumWageByGenderRange: '',

    // Parental leave
    tookParentalLeave: '',
    returnParentalLeaveCount: '',
    stillEmployedCount: '',
    rateOfTookParentalLeave: '',

    // Incidents of discrimination
    totalIncidentsOfDiscrimination: '',
    reviewedIncidentsCount: '',
    remediationPlansBeingImplemented: '',
    remediationPlansImplemented: '',
    incidentsNoLongerSubjectToAction: '',
    staffTrainingConducted: '',
    unconsciousBiasTrainingConducted: '',

    // Health and Safety
    healthAndSafetyManagementSystem: '',
    healthAndSafetyManagementSystemImplemented: '',
    healthAndSafetyManagementSystemImplementedLegalRequirements: '',
    healthAndSafetyManagementSystemImplementedRiskManagement: '',
    healthAndSafetyManagementSystemScope: '',
    healthAndSafetyManagementSystemWorkersNotCovered: '',
    healthAndSafetyManagementSystemBestPracticeProgrammes: '',
    healthAndSafetyManagementSystemWorkRelatedIllHealth: '',

    // Skill Development
    scopeOfProgrammes: '',
    specifyAssistancePrograms: '',

    // Employee reviews
    femaleEmployeeReviewsGenderRange: '',
    maleEmployeeReviewsGenderRange: '',
    femaleLeadershipEmployeeReviewsCategoryRange: '',
    femaleManagementEmployeeReviewsCategoryRange: '',
    femaleTechnicalEmployeeReviewsCategoryRange: '',
    femaleOtherEmployeeReviewsCategoryRange: '',
    maleLeadershipEmployeeReviewsCategoryRange: '',
    maleManagementEmployeeReviewsCategoryRange: '',
    maleTechnicalEmployeeReviewsCategoryRange: '',
    maleOtherEmployeeReviewsCategoryRange: '',

    // Products and services
    detailOnProduct: '',
    descriptionOfPolicies: '',
    organizationImplementIntiatives: [],

    // Supplier due diligence
    numberSuppliersAssessed: '',
    numberSuppliersSignificantImpacts: '',
    numberSuppliersImprovementsAgreed: '',
    numberSuppliersRelationshipTerminated: '',
    significantNegativeSocialImpacts: '',

    // Certifications
    diversityAndInclusionCertifications: '',
    healthAndSafetyCertifications: '',

    // Monetized impacts of training
    increasedRevenueDueToTraining: '',
    productivityGainsDueToTraining: '',
    employeeEngagementDueToTraining: '',
    internalHireRates: '',

    // Innovation of better products and services
    benefitsOnRevenueFromServices: '',
    techForGoodRecognitions: '',

    // Local community engagement
    socialImpactAssessments: '',
    publicDisclosureOfResults: '',
    workerRepresentationBodies: '',
    localCommunityDevelopmentPrograms: '',
    stakeholderEngagementPlans: '',
    formalGrievanceProcesses: '',
  });

  const [submitted, setSubmitted] = useState(false);
  const [clearDialogOpen, setClearDialogOpen] = useState(false);

  const handleInputChange = (field: keyof FormData) => (event: any) => {
    const value = event.target.value;
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleCheckboxChange = (field: keyof FormData) => (event: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      [field]: event.target.checked
    }));
  };

  const handleMultiSelectChange = (field: keyof FormData, option: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => {
      const currentArray = prev[field] as string[];
      return {
        ...prev,
        [field]: event.target.checked
          ? [...currentArray, option]
          : currentArray.filter(item => item !== option)
      };
    });
  };

  const handleNext = () => {
    if (currentSection < sections.length - 1) {
      setCurrentSection(currentSection + 1);
    }
  };

  const handlePrevious = () => {
    if (currentSection > 0) {
      setCurrentSection(currentSection - 1);
    }
  };

  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    console.log('Form submitted:', formData);
    setSubmitted(true);
  };

  const handleClearFormClick = () => {
    setClearDialogOpen(true);
  };

  const handleClearDialogClose = () => {
    setClearDialogOpen(false);
  };

  const handleConfirmClearForm = () => {
    setCurrentSection(0);
    setFormData({
      // General Information
      companyName: '',

      // Energy Consumption
      energyConsumptionType: '',
      renewableEnergyElements: [],
      nonRenewableEnergyElements: [],
      windElectricity: '',
      solarElectricity: '',
      otherRenewableElectricity: '',
      gridElectricityConsumed: '',
      fuelConsumed: '',
      otherEnergyConsumed: '',
      totalEnergyConsumed: '',
      gridEnergyPercentage: '',
      renewablePercentage: '',
      numberOfEmployees: '',
      energyPerEmployee: '',

      // Water Consumption
      waterSource: [],
      totalWaterWithdrawn: '',
      totalWaterConsumed: '',
      waterReused: '',
      highWaterStressRegions: '',
      waterWithdrawnStress: '',
      waterConsumedStress: '',

      // Emissions
      emissionSources: [],
      dieselUnits: '',
      dieselCO2: '',
      petrolUnits: '',
      petrolCO2: '',
      keroseneUnits: '',
      keroseneCO2: '',
      electricityUnits: '',
      electricityCO2: '',

      // Waste Management
      wasteActions: [],
      ewasteQuantity: '',
      ewasteHazardous: '',
      paperWasteQuantity: '',
      paperWasteHazardous: '',
      foodWasteQuantity: '',
      foodWasteHazardous: '',
      paperRecycledQuantity: '',
      paperRecycledPercentage: '',
      plasticRecycledQuantity: '',
      plasticRecycledPercentage: '',
      organicWasteQuantity: '',
      landfillWaste: '',
      landfillQuantity: '',

      // GHG Emissions
      naturalGas: '',
      distillateFuelOil: '',
      gasoline: '',
      refrigerants: '',
      purchasedElectricity: '',
      heatingCooling: '',
      travel: '',
      purchasedGoods: '',
      upstreamTransportation: '',
      wasteFromOperations: '',

      // Compliance
      ehsPractices: '',
      nonCompliance: '',
      finesPenalties: '',
      nonComplianceDetails: '',
      codeOfConduct: '',
      environmentalRegulations: '',

      // Assessments
      supplierRiskAssessment: '',
      supplierAssessmentFrequency: '',
      supplierAudits: '',
      correctiveActionPlans: '',
      carbonReductionTargets: '',

      // Financial Contributions
      financialContributions: '',
      directFinancialValue: '',
      indirectFinancialValue: '',
      inKindContributions: '',
      directInKindValue: '',
      indirectInKindValue: '',
      inKindEstimation: '',
      sbtiTargets: '',
      sbtiDescription: '',

      // Gender Representation
      femaleGenderLeadershipRange: '',
      maleGenderLeadershipRange: '',
      femaleGenderManagementRange: '',
      maleGenderManagementRange: '',
      femaleGenderTechnicalRange: '',
      maleGenderTechnicalRange: '',

      // Age Group Representation
      femaleAgeLeadershipRange: '',
      maleAgeLeadershipRange: '',
      femaleAgeManagementRange: '',
      maleAgeManagementRange: '',
      femaleAgeTechnicalRange: '',
      maleAgeTechnicalRange: '',

      // Disability Representation
      femaleDisabilityLeadershipRange: '',
      maleDisabilityLeadershipRange: '',
      femaleDisabilityManagementRange: '',
      maleDisabilityManagementRange: '',
      femaleDisabilityTechnicalRange: '',
      maleDisabilityTechnicalRange: '',

      // Ethnicity Female Representation
      sinhalaFemaleRange: '',
      tamilFemaleRange: '',
      moorsFemaleRange: '',
      burgherFemaleRange: '',
      otherFemaleRange: '',

      // Ethnicity Male Representation
      sinhalaMaleRange: '',
      tamilMaleRange: '',
      moorsMaleRange: '',
      burgherMaleRange: '',
      otherMaleRange: '',

      // individuals within the organization’s governance
      femaleIndividualGenderRange: '',
      maleIndividualGenderRange: '',
      femaleIndividualAgeRange: '',
      maleIndividualAgeRange: '',

      // employee turnover
      femaleEmployeeTurnoverGenderRange: '',
      maleEmployeeTurnoverGenderRange: '',
      femaleEmployeeTurnoverAgeRange: '',
      maleEmployeeTurnoverAgeRange: '',
      leadershipEmployeeTurnoverCategoryRange: '',
      managementEmployeeTurnoverCategoryRange: '',
      technicalEmployeeTurnoverCategoryRange: '',
      otherEmployeeTurnoverCategoryRange: '',

      // new employee hires
      femaleNewEmployeeHiresGenderRange: '',
      maleNewEmployeeHiresGenderRange: '',
      femaleNewEmployeeHiresAgeRange: '',
      maleNewEmployeeHiresAgeRange: '',
      leadershipNewEmployeeHiresCategoryRange: '',
      managementNewEmployeeHiresCategoryRange: '',
      technicalNewEmployeeHiresCategoryRange: '',
      otherNewEmployeeHiresCategoryRange: '',
      nonOrganizationControlledWorkers: '',

      // labor relations
      haveHRPolicy: '',
      hrPoliciesInclude: '',
      policiesCommunicated: '',

      // Child forced or compulsory labour
      ageVerificationMechanism: '',
      childLabourRiskPolicy: '',

      // Health and Well-being
      haveHealthAndSafetyPolicy: '',
      healthPoliciesInclude: '',

      // skill development
      haveTrainingPolicies: '',
      describeTrainingPolicy: '',
      haveSkillDevelopmentTraining: '',
      femaleTrainingHoursGenderRange: '',
      maleTrainingHoursGenderRange: '',
      leadershipTrainingHoursCategoryRange: '',
      managementTrainingHoursCategoryRange: '',
      technicalTrainingHoursCategoryRange: '',
      otherTrainingHoursCategoryRange: '',

      // products and services
      haveTechForGoodPolicy: '',
      describeTechForGoodPolicy: '',
      researchAndDevelopmentCost: '',

      // Community and Social vitality
      haveDataSecurityPolicy: '',
      describeDataSecurityPolicy: '',
      substantiatedComplaintsReceived: '',
      identifiedLossesOfData: '',
      usersAffectedDataBreaches: '',

      // Community Engagement
      organizationEngagementCommunities: '',
      haveSupplierSocialPolicy: '',
      supplierDueDiligencePolicy: '',

      // Pay Gap
      femaleLeadershipBasicSalleryByGenderRange: '',
      femaleManagementBasicSalleryByGenderRange: '',
      femaleTechnicalBasicSalleryByGenderRange: '',
      femaleOtherBasicSalleryByGenderRange: '',
      maleLeadershipBasicSalleryByGenderRange: '',
      maleManagementBasicSalleryByGenderRange: '',
      maleTechnicalBasicSalleryByGenderRange: '',
      maleOtherBasicSalleryByGenderRange: '',
      femaleLeadershipMinimumWageByGenderRange: '',
      femaleManagementMinimumWageByGenderRange: '',
      femaleTechnicalMinimumWageByGenderRange: '',
      femaleOtherMinimumWageByGenderRange: '',
      maleLeadershipMinimumWageByGenderRange: '',
      maleManagementMinimumWageByGenderRange: '',
      maleTechnicalMinimumWageByGenderRange: '',
      maleOtherMinimumWageByGenderRange: '',

      // Parental leave
      tookParentalLeave: '',
      returnParentalLeaveCount: '',
      stillEmployedCount: '',
      rateOfTookParentalLeave: '',

      // Incidents of discrimination
      totalIncidentsOfDiscrimination: '',
      reviewedIncidentsCount: '',
      remediationPlansBeingImplemented: '',
      remediationPlansImplemented: '',
      incidentsNoLongerSubjectToAction: '',
      staffTrainingConducted: '',
      unconsciousBiasTrainingConducted: '',

      // Health and Safety
      healthAndSafetyManagementSystem: '',
      healthAndSafetyManagementSystemImplemented: '',
      healthAndSafetyManagementSystemImplementedLegalRequirements: '',
      healthAndSafetyManagementSystemImplementedRiskManagement: '',
      healthAndSafetyManagementSystemScope: '',
      healthAndSafetyManagementSystemWorkersNotCovered: '',
      healthAndSafetyManagementSystemBestPracticeProgrammes: '',
      healthAndSafetyManagementSystemWorkRelatedIllHealth: '',

      // Skill Development
      scopeOfProgrammes: '',
      specifyAssistancePrograms: '',

      // Employee reviews
      femaleEmployeeReviewsGenderRange: '',
      maleEmployeeReviewsGenderRange: '',
      femaleLeadershipEmployeeReviewsCategoryRange: '',
      femaleManagementEmployeeReviewsCategoryRange: '',
      femaleTechnicalEmployeeReviewsCategoryRange: '',
      femaleOtherEmployeeReviewsCategoryRange: '',
      maleLeadershipEmployeeReviewsCategoryRange: '',
      maleManagementEmployeeReviewsCategoryRange: '',
      maleTechnicalEmployeeReviewsCategoryRange: '',
      maleOtherEmployeeReviewsCategoryRange: '',

      // Products and services
      detailOnProduct: '',
      descriptionOfPolicies: '',
      organizationImplementIntiatives: [],

      // Supplier due diligence
      numberSuppliersAssessed: '',
      numberSuppliersSignificantImpacts: '',
      numberSuppliersImprovementsAgreed: '',
      numberSuppliersRelationshipTerminated: '',
      significantNegativeSocialImpacts: '',

      // Certifications
      diversityAndInclusionCertifications: '',
      healthAndSafetyCertifications: '',

      // Monetized impacts of training
      increasedRevenueDueToTraining: '',
      productivityGainsDueToTraining: '',
      employeeEngagementDueToTraining: '',
      internalHireRates: '',

      // Innovation of better products and services
      benefitsOnRevenueFromServices: '',
      techForGoodRecognitions: '',

      // Local community engagement
      socialImpactAssessments: '',
      publicDisclosureOfResults: '',
      workerRepresentationBodies: '',
      localCommunityDevelopmentPrograms: '',
      stakeholderEngagementPlans: '',
      formalGrievanceProcesses: '',
    });
    setSubmitted(false);
    setClearDialogOpen(false);
  };

  // Keep the original resetForm function for the "Submit Another Response" button
  const resetForm = () => {
    setCurrentSection(0);
    setFormData({
      // General Information
      companyName: '',

      // Energy Consumption
      energyConsumptionType: '',
      renewableEnergyElements: [],
      nonRenewableEnergyElements: [],
      windElectricity: '',
      solarElectricity: '',
      otherRenewableElectricity: '',
      gridElectricityConsumed: '',
      fuelConsumed: '',
      otherEnergyConsumed: '',
      totalEnergyConsumed: '',
      gridEnergyPercentage: '',
      renewablePercentage: '',
      numberOfEmployees: '',
      energyPerEmployee: '',

      // Water Consumption
      waterSource: [],
      totalWaterWithdrawn: '',
      totalWaterConsumed: '',
      waterReused: '',
      highWaterStressRegions: '',
      waterWithdrawnStress: '',
      waterConsumedStress: '',

      // Emissions
      emissionSources: [],
      dieselUnits: '',
      dieselCO2: '',
      petrolUnits: '',
      petrolCO2: '',
      keroseneUnits: '',
      keroseneCO2: '',
      electricityUnits: '',
      electricityCO2: '',

      // Waste Management
      wasteActions: [],
      ewasteQuantity: '',
      ewasteHazardous: '',
      paperWasteQuantity: '',
      paperWasteHazardous: '',
      foodWasteQuantity: '',
      foodWasteHazardous: '',
      paperRecycledQuantity: '',
      paperRecycledPercentage: '',
      plasticRecycledQuantity: '',
      plasticRecycledPercentage: '',
      organicWasteQuantity: '',
      landfillWaste: '',
      landfillQuantity: '',

      // GHG Emissions
      naturalGas: '',
      distillateFuelOil: '',
      gasoline: '',
      refrigerants: '',
      purchasedElectricity: '',
      heatingCooling: '',
      travel: '',
      purchasedGoods: '',
      upstreamTransportation: '',
      wasteFromOperations: '',

      // Compliance
      ehsPractices: '',
      nonCompliance: '',
      finesPenalties: '',
      nonComplianceDetails: '',
      codeOfConduct: '',
      environmentalRegulations: '',

      // Assessments
      supplierRiskAssessment: '',
      supplierAssessmentFrequency: '',
      supplierAudits: '',
      correctiveActionPlans: '',
      carbonReductionTargets: '',

      // Financial Contributions
      financialContributions: '',
      directFinancialValue: '',
      indirectFinancialValue: '',
      inKindContributions: '',
      directInKindValue: '',
      indirectInKindValue: '',
      inKindEstimation: '',
      sbtiTargets: '',
      sbtiDescription: '',

      // Gender Representation
      femaleGenderLeadershipRange: '',
      maleGenderLeadershipRange: '',
      femaleGenderManagementRange: '',
      maleGenderManagementRange: '',
      femaleGenderTechnicalRange: '',
      maleGenderTechnicalRange: '',

      // Age Group Representation
      femaleAgeLeadershipRange: '',
      maleAgeLeadershipRange: '',
      femaleAgeManagementRange: '',
      maleAgeManagementRange: '',
      femaleAgeTechnicalRange: '',
      maleAgeTechnicalRange: '',

      // Disability Representation
      femaleDisabilityLeadershipRange: '',
      maleDisabilityLeadershipRange: '',
      femaleDisabilityManagementRange: '',
      maleDisabilityManagementRange: '',
      femaleDisabilityTechnicalRange: '',
      maleDisabilityTechnicalRange: '',

      // Ethnicity Female Representation
      sinhalaFemaleRange: '',
      tamilFemaleRange: '',
      moorsFemaleRange: '',
      burgherFemaleRange: '',
      otherFemaleRange: '',

      // Ethnicity Male Representation
      sinhalaMaleRange: '',
      tamilMaleRange: '',
      moorsMaleRange: '',
      burgherMaleRange: '',
      otherMaleRange: '',

      // individuals within the organization’s governance
      femaleIndividualGenderRange: '',
      maleIndividualGenderRange: '',
      femaleIndividualAgeRange: '',
      maleIndividualAgeRange: '',

      // employee turnover
      femaleEmployeeTurnoverGenderRange: '',
      maleEmployeeTurnoverGenderRange: '',
      femaleEmployeeTurnoverAgeRange: '',
      maleEmployeeTurnoverAgeRange: '',
      leadershipEmployeeTurnoverCategoryRange: '',
      managementEmployeeTurnoverCategoryRange: '',
      technicalEmployeeTurnoverCategoryRange: '',
      otherEmployeeTurnoverCategoryRange: '',

      // new employee hires
      femaleNewEmployeeHiresGenderRange: '',
      maleNewEmployeeHiresGenderRange: '',
      femaleNewEmployeeHiresAgeRange: '',
      maleNewEmployeeHiresAgeRange: '',
      leadershipNewEmployeeHiresCategoryRange: '',
      managementNewEmployeeHiresCategoryRange: '',
      technicalNewEmployeeHiresCategoryRange: '',
      otherNewEmployeeHiresCategoryRange: '',
      nonOrganizationControlledWorkers: '',

      // labor relations
      haveHRPolicy: '',
      hrPoliciesInclude: '',
      policiesCommunicated: '',

      // Child forced or compulsory labour
      ageVerificationMechanism: '',
      childLabourRiskPolicy: '',

      // Health and Well-being
      haveHealthAndSafetyPolicy: '',
      healthPoliciesInclude: '',

      // skill development
      haveTrainingPolicies: '',
      describeTrainingPolicy: '',
      haveSkillDevelopmentTraining: '',
      femaleTrainingHoursGenderRange: '',
      maleTrainingHoursGenderRange: '',
      leadershipTrainingHoursCategoryRange: '',
      managementTrainingHoursCategoryRange: '',
      technicalTrainingHoursCategoryRange: '',
      otherTrainingHoursCategoryRange: '',

      // products and services
      haveTechForGoodPolicy: '',
      describeTechForGoodPolicy: '',
      researchAndDevelopmentCost: '',

      // Community and Social vitality
      haveDataSecurityPolicy: '',
      describeDataSecurityPolicy: '',
      substantiatedComplaintsReceived: '',
      identifiedLossesOfData: '',
      usersAffectedDataBreaches: '',

      // Community Engagement
      organizationEngagementCommunities: '',
      haveSupplierSocialPolicy: '',
      supplierDueDiligencePolicy: '',

      // Pay Gap
      femaleLeadershipBasicSalleryByGenderRange: '',
      femaleManagementBasicSalleryByGenderRange: '',
      femaleTechnicalBasicSalleryByGenderRange: '',
      femaleOtherBasicSalleryByGenderRange: '',
      maleLeadershipBasicSalleryByGenderRange: '',
      maleManagementBasicSalleryByGenderRange: '',
      maleTechnicalBasicSalleryByGenderRange: '',
      maleOtherBasicSalleryByGenderRange: '',
      femaleLeadershipMinimumWageByGenderRange: '',
      femaleManagementMinimumWageByGenderRange: '',
      femaleTechnicalMinimumWageByGenderRange: '',
      femaleOtherMinimumWageByGenderRange: '',
      maleLeadershipMinimumWageByGenderRange: '',
      maleManagementMinimumWageByGenderRange: '',
      maleTechnicalMinimumWageByGenderRange: '',
      maleOtherMinimumWageByGenderRange: '',

      // Parental leave
      tookParentalLeave: '',
      returnParentalLeaveCount: '',
      stillEmployedCount: '',
      rateOfTookParentalLeave: '',

      // Incidents of discrimination
      totalIncidentsOfDiscrimination: '',
      reviewedIncidentsCount: '',
      remediationPlansBeingImplemented: '',
      remediationPlansImplemented: '',
      incidentsNoLongerSubjectToAction: '',
      staffTrainingConducted: '',
      unconsciousBiasTrainingConducted: '',

      // Health and Safety
      healthAndSafetyManagementSystem: '',
      healthAndSafetyManagementSystemImplemented: '',
      healthAndSafetyManagementSystemImplementedLegalRequirements: '',
      healthAndSafetyManagementSystemImplementedRiskManagement: '',
      healthAndSafetyManagementSystemScope: '',
      healthAndSafetyManagementSystemWorkersNotCovered: '',
      healthAndSafetyManagementSystemBestPracticeProgrammes: '',
      healthAndSafetyManagementSystemWorkRelatedIllHealth: '',

      // Skill Development
      scopeOfProgrammes: '',
      specifyAssistancePrograms: '',

      // Employee reviews
      femaleEmployeeReviewsGenderRange: '',
      maleEmployeeReviewsGenderRange: '',
      femaleLeadershipEmployeeReviewsCategoryRange: '',
      femaleManagementEmployeeReviewsCategoryRange: '',
      femaleTechnicalEmployeeReviewsCategoryRange: '',
      femaleOtherEmployeeReviewsCategoryRange: '',
      maleLeadershipEmployeeReviewsCategoryRange: '',
      maleManagementEmployeeReviewsCategoryRange: '',
      maleTechnicalEmployeeReviewsCategoryRange: '',
      maleOtherEmployeeReviewsCategoryRange: '',

      // Products and services
      detailOnProduct: '',
      descriptionOfPolicies: '',
      organizationImplementIntiatives: [],

      // Supplier due diligence
      numberSuppliersAssessed: '',
      numberSuppliersSignificantImpacts: '',
      numberSuppliersImprovementsAgreed: '',
      numberSuppliersRelationshipTerminated: '',
      significantNegativeSocialImpacts: '',

      // Certifications
      diversityAndInclusionCertifications: '',
      healthAndSafetyCertifications: '',

      // Monetized impacts of training
      increasedRevenueDueToTraining: '',
      productivityGainsDueToTraining: '',
      employeeEngagementDueToTraining: '',
      internalHireRates: '',

      // Innovation of better products and services
      benefitsOnRevenueFromServices: '',
      techForGoodRecognitions: '',

      // Local community engagement
      socialImpactAssessments: '',
      publicDisclosureOfResults: '',
      workerRepresentationBodies: '',
      localCommunityDevelopmentPrograms: '',
      stakeholderEngagementPlans: '',
      formalGrievanceProcesses: '',
    });
    setSubmitted(false);
  };

  if (submitted) {
    return (
      <Box>
        {/* Header Bar */}
        <AppBar position="static" sx={{ bgcolor: '#A6B28B', mb: { xs: 2, sm: 4 } }}>
          <Toolbar sx={{
            display: 'flex',
            alignItems: 'center',
            flexDirection: { xs: 'column', sm: 'row' },
            py: { xs: 0.5, sm: 1 },
            gap: { xs: 0.5, sm: 0 },
            minHeight: { xs: 'auto', sm: '64px' }
          }}>
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              mr: { xs: 0, sm: 2 },
              order: { xs: 1, sm: 1 }
            }}>
              <Box
                component="img"
                src={logo}
                alt="SLASSCOM Logo"
                sx={{
                  height: { xs: '24px', sm: '40px' },
                  width: 'auto',
                  mr: { xs: 1, sm: 2 }
                }}
              />
            </Box>
            <Typography
              variant="h6"
              component="div"
              sx={{
                flexGrow: 1,
                fontWeight: 'bold',
                textAlign: 'center',
                fontSize: { xs: '0.9rem', sm: '1.25rem' },
                order: { xs: 2, sm: 2 },
                py: { xs: 0.5, sm: 0 }
              }}
            >
              SLASSCOM - Baseline Matrix
            </Typography>
            {/* Spacer to balance the logo on the left */}
            <Box sx={{ width: { xs: 0, sm: '56px' }, order: { xs: 3, sm: 3 } }} />
          </Toolbar>
        </AppBar>

        <Container maxWidth="md" sx={{ py: { xs: 2, sm: 4 }, px: { xs: 2, sm: 3 } }}>
          <Paper elevation={3} sx={{
            p: { xs: 3, sm: 4 },
            textAlign: 'center',
            bgcolor: '#ECEEDF'
          }}>
            <Alert severity="success" sx={{ mb: 3 }}>
              Thank you for submitting the SLASSCOM Baseline Matrix form!
            </Alert>
            <Typography
              variant="h5"
              gutterBottom
              sx={{ fontSize: { xs: '1.25rem', sm: '1.5rem' } }}
            >
              Form Submitted Successfully
            </Typography>
            <Typography
              variant="body1"
              sx={{
                mb: 3,
                fontSize: { xs: '0.875rem', sm: '1rem' }
              }}
            >
              We have received your environmental baseline data and will process it accordingly.
            </Typography>
            <Button
              variant="contained"
              color="success"
              onClick={resetForm}
              sx={{
                px: { xs: 3, sm: 4 },
                py: { xs: 1, sm: 1.5 }
              }}
            >
              Submit Another Response
            </Button>
          </Paper>
        </Container>
      </Box>
    );
  }

  const renderGenderRepresentation = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Gender - Female and Male Representation in Leadership
      </Typography>
      {/* <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Please indicate the percentage ranges for gender representation in leadership positions.
      </Typography> */}

      <Box sx={{ mb: 4 }}>
        {/* Header Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box sx={{ width: '120px' }}></Box>
          <Box sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              0 - 25%
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              26% - 50%
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              51% - 75%
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              76% - 100%
            </Typography>
          </Box>
        </Box>

        {/* Female Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Female
          </Typography>
          <RadioGroup
            row
            value={formData.femaleGenderLeadershipRange}
            onChange={handleInputChange('femaleGenderLeadershipRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="51-75"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="76-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>

        {/* Male Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Male
          </Typography>
          <RadioGroup
            row
            value={formData.maleGenderLeadershipRange}
            onChange={handleInputChange('maleGenderLeadershipRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="51-75"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="76-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>
      </Box>

      <Typography variant="h6" gutterBottom>
        Gender - Female and Male Representation in Management
      </Typography>
      {/* <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Please indicate the percentage ranges for gender representation in leadership positions.
      </Typography> */}

      <Box sx={{ mb: 4 }}>
        {/* Header Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box sx={{ width: '120px' }}></Box>
          <Box sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              0 - 25%
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              26% - 50%
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              51% - 75%
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              76% - 100%
            </Typography>
          </Box>
        </Box>

        {/* Female Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Female
          </Typography>
          <RadioGroup
            row
            value={formData.femaleGenderManagementRange}
            onChange={handleInputChange('femaleGenderManagementRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="51-75"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="76-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>

        {/* Male Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Male
          </Typography>
          <RadioGroup
            row
            value={formData.maleGenderManagementRange}
            onChange={handleInputChange('maleGenderManagementRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="51-75"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="76-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>
      </Box>

      <Typography variant="h6" gutterBottom>
        Gender - Female and Male Representation in Technical
      </Typography>
      {/* <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Please indicate the percentage ranges for gender representation in leadership positions.
      </Typography> */}

      <Box sx={{ mb: 4 }}>
        {/* Header Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box sx={{ width: '120px' }}></Box>
          <Box sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              0 - 25%
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              26% - 50%
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              51% - 75%
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              76% - 100%
            </Typography>
          </Box>
        </Box>

        {/* Female Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Female
          </Typography>
          <RadioGroup
            row
            value={formData.femaleGenderTechnicalRange}
            onChange={handleInputChange('femaleGenderTechnicalRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="51-75"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="76-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>

        {/* Male Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Male
          </Typography>
          <RadioGroup
            row
            value={formData.maleGenderTechnicalRange}
            onChange={handleInputChange('maleGenderTechnicalRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="51-75"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="76-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>
      </Box>
    </Box>
  );

  const renderAgeRepresentation = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Age Group - Female and Male Representation in Leadership
      </Typography>
      {/* <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Please indicate the percentage ranges for gender representation in leadership positions.
      </Typography> */}

      <Box sx={{ mb: 4 }}>
        {/* Header Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box sx={{ width: '120px' }}></Box>
          <Box sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              Under 30 years
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              30-50 years old
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              Over 50 years old
            </Typography>
          </Box>
        </Box>

        {/* Female Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Female
          </Typography>
          <RadioGroup
            row
            value={formData.femaleAgeLeadershipRange}
            onChange={handleInputChange('femaleAgeLeadershipRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-30"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="30-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="50-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>

        {/* Male Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Male
          </Typography>
          <RadioGroup
            row
            value={formData.maleAgeLeadershipRange}
            onChange={handleInputChange('maleAgeLeadershipRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-30"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="30-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="50-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>
      </Box>

      <Typography variant="h6" gutterBottom>
        Age Group - Female and Male Representation in Management
      </Typography>
      {/* <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Please indicate the percentage ranges for gender representation in leadership positions.
      </Typography> */}

      <Box sx={{ mb: 4 }}>
        {/* Header Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box sx={{ width: '120px' }}></Box>
          <Box sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              Under 30 years
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              30-50 years old
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              Over 50 years old
            </Typography>
          </Box>
        </Box>

        {/* Female Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Female
          </Typography>
          <RadioGroup
            row
            value={formData.femaleAgeManagementRange}
            onChange={handleInputChange('femaleAgeManagementRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-30"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="30-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="50-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>

        {/* Male Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Male
          </Typography>
          <RadioGroup
            row
            value={formData.maleAgeManagementRange}
            onChange={handleInputChange('maleAgeManagementRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-30"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="30-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="50-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>
      </Box>

      <Typography variant="h6" gutterBottom>
        Age Group - Female and Male Representation in Technical
      </Typography>
      {/* <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Please indicate the percentage ranges for gender representation in leadership positions.
      </Typography> */}

      <Box sx={{ mb: 4 }}>
        {/* Header Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box sx={{ width: '120px' }}></Box>
          <Box sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              Under 30 years
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              30-50 years old
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              Over 50 years old
            </Typography>
          </Box>
        </Box>

        {/* Female Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Female
          </Typography>
          <RadioGroup
            row
            value={formData.femaleAgeTechnicalRange}
            onChange={handleInputChange('femaleAgeTechnicalRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-30"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="30-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="50-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>

        {/* Male Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Male
          </Typography>
          <RadioGroup
            row
            value={formData.maleAgeTechnicalRange}
            onChange={handleInputChange('maleAgeTechnicalRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-30"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="30-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="50-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>
      </Box>
    </Box>
  );

  const renderDisabilityRepresentation = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Disability - Female and Male Representation in Leadership
      </Typography>
      {/* <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Please indicate the percentage ranges for gender representation in leadership positions.
      </Typography> */}

      <Box sx={{ mb: 4 }}>
        {/* Header Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box sx={{ width: '120px' }}></Box>
          <Box sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              None
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              Less Than 10
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              10 - 25
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              26 - 40
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              41 - 55
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              55 - 70
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              71 - 85
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              86 - 100
            </Typography>
          </Box>
        </Box>

        {/* Female Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Female
          </Typography>
          <RadioGroup
            row
            value={formData.femaleDisabilityLeadershipRange}
            onChange={handleInputChange('femaleDisabilityLeadershipRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="0-10"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="11-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-40"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="41-55"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="55-70"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="71-85"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="86-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>

        {/* Male Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Male
          </Typography>
          <RadioGroup
            row
            value={formData.maleDisabilityLeadershipRange}
            onChange={handleInputChange('maleDisabilityLeadershipRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="0-10"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="11-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-40"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="41-55"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="55-70"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="71-85"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="86-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>
      </Box>

      <Typography variant="h6" gutterBottom>
        Disability - Female and Male Representation in Management
      </Typography>
      {/* <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Please indicate the percentage ranges for gender representation in leadership positions.
      </Typography> */}

      <Box sx={{ mb: 4 }}>
        {/* Header Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box sx={{ width: '120px' }}></Box>
          <Box sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              None
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              Less Than 10
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              10 - 25
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              26 - 40
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              41 - 55
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              55 - 70
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              71 - 85
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              86 - 100
            </Typography>
          </Box>
        </Box>

        {/* Female Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Female
          </Typography>
          <RadioGroup
            row
            value={formData.femaleDisabilityManagementRange}
            onChange={handleInputChange('femaleDisabilityManagementRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="0-10"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="11-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-40"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="41-55"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="55-70"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="71-85"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="86-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>

        {/* Male Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Male
          </Typography>
          <RadioGroup
            row
            value={formData.maleDisabilityManagementRange}
            onChange={handleInputChange('maleDisabilityManagementRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="0-10"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="11-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-40"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="41-55"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="55-70"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="71-85"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="86-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>
      </Box>

      <Typography variant="h6" gutterBottom>
        Disability - Female and Male Representation in Technical
      </Typography>
      {/* <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Please indicate the percentage ranges for gender representation in leadership positions.
      </Typography> */}

      <Box sx={{ mb: 4 }}>
        {/* Header Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box sx={{ width: '120px' }}></Box>
          <Box sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              None
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              Less Than 10
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              10 - 25
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              26 - 40
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              41 - 55
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              55 - 70
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              71 - 85
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              86 - 100
            </Typography>
          </Box>
        </Box>

        {/* Female Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Female
          </Typography>
          <RadioGroup
            row
            value={formData.femaleDisabilityTechnicalRange}
            onChange={handleInputChange('femaleDisabilityTechnicalRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="0-10"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="11-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-40"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="41-55"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="55-70"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="71-85"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="86-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>

        {/* Male Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Male
          </Typography>
          <RadioGroup
            row
            value={formData.maleDisabilityTechnicalRange}
            onChange={handleInputChange('maleDisabilityTechnicalRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="0-10"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="11-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-40"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="41-55"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="55-70"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="71-85"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="86-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>
      </Box>
    </Box>
  );

  const renderEthnicityRepresentation = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Ethnicity  - Female Representation
      </Typography>
      {/* <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Please indicate the percentage ranges for gender representation in leadership positions.
      </Typography> */}

      <Box sx={{ mb: 4 }}>
        {/* Header Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box sx={{ width: '120px' }}></Box>
          <Box sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              0 - 25%
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              26% - 50%
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              51% - 75%
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              76% - 100%
            </Typography>
          </Box>
        </Box>

        {/* Sinhala Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Sinhala
          </Typography>
          <RadioGroup
            row
            value={formData.sinhalaFemaleRange}
            onChange={handleInputChange('sinhalaFemaleRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="51-75"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="76-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>

        {/* Tamil Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Tamil
          </Typography>
          <RadioGroup
            row
            value={formData.tamilFemaleRange}
            onChange={handleInputChange('tamilFemaleRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="51-75"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="76-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>

        {/* Moors Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Moors
          </Typography>
          <RadioGroup
            row
            value={formData.moorsFemaleRange}
            onChange={handleInputChange('moorsFemaleRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="51-75"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="76-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>

        {/* Burgher Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Burgher
          </Typography>
          <RadioGroup
            row
            value={formData.burgherFemaleRange}
            onChange={handleInputChange('burgherFemaleRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="51-75"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="76-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>

        {/* Other Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Other
          </Typography>
          <RadioGroup
            row
            value={formData.otherFemaleRange}
            onChange={handleInputChange('otherFemaleRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="51-75"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="76-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>
      </Box>

      <Typography variant="h6" gutterBottom>
        Ethnicity  - Male Representation
      </Typography>
      {/* <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Please indicate the percentage ranges for gender representation in leadership positions.
      </Typography> */}

      <Box sx={{ mb: 4 }}>
        {/* Header Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box sx={{ width: '120px' }}></Box>
          <Box sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              0 - 25%
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              26% - 50%
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              51% - 75%
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              76% - 100%
            </Typography>
          </Box>
        </Box>

        {/* Sinhala Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Sinhala
          </Typography>
          <RadioGroup
            row
            value={formData.sinhalaMaleRange}
            onChange={handleInputChange('sinhalaMaleRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="51-75"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="76-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>

        {/* Tamil Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Tamil
          </Typography>
          <RadioGroup
            row
            value={formData.tamilMaleRange}
            onChange={handleInputChange('tamilMaleRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="51-75"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="76-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>

        {/* Moors Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Moors
          </Typography>
          <RadioGroup
            row
            value={formData.moorsMaleRange}
            onChange={handleInputChange('moorsMaleRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="51-75"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="76-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>

        {/* Burgher Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Burgher
          </Typography>
          <RadioGroup
            row
            value={formData.burgherMaleRange}
            onChange={handleInputChange('burgherMaleRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="51-75"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="76-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>

        {/* Other Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Other
          </Typography>
          <RadioGroup
            row
            value={formData.otherMaleRange}
            onChange={handleInputChange('otherMaleRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="51-75"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="76-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>
      </Box>
    </Box>
  );

  const renderIndividualsRepresentation = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Individuals within the organization’s governance bodies  as per the Gender
      </Typography>
      {/* <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Please indicate the percentage ranges for gender representation in leadership positions.
      </Typography> */}

      <Box sx={{ mb: 4 }}>
        {/* Header Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box sx={{ width: '120px' }}></Box>
          <Box sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              0 - 25%
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              26% - 50%
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              51% - 75%
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              76% - 100%
            </Typography>
          </Box>
        </Box>

        {/* Female Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Female
          </Typography>
          <RadioGroup
            row
            value={formData.femaleIndividualGenderRange}
            onChange={handleInputChange('femaleIndividualGenderRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="51-75"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="76-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>

        {/* Male Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Male
          </Typography>
          <RadioGroup
            row
            value={formData.maleIndividualGenderRange}
            onChange={handleInputChange('maleIndividualGenderRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="51-75"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="76-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>
      </Box>

      <Typography variant="h6" gutterBottom>
        Individuals within the organization’s governance bodies  as per the Age group
      </Typography>
      {/* <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Please indicate the percentage ranges for gender representation in leadership positions.
      </Typography> */}

      <Box sx={{ mb: 4 }}>
        {/* Header Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box sx={{ width: '120px' }}></Box>
          <Box sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              0 - 25%
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              26% - 50%
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              51% - 75%
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              76% - 100%
            </Typography>
          </Box>
        </Box>

        {/* Female Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Female
          </Typography>
          <RadioGroup
            row
            value={formData.femaleIndividualAgeRange}
            onChange={handleInputChange('femaleIndividualAgeRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="51-75"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="76-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>

        {/* Male Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Male
          </Typography>
          <RadioGroup
            row
            value={formData.maleIndividualAgeRange}
            onChange={handleInputChange('maleIndividualAgeRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="51-75"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="76-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>
      </Box>
    </Box>
  );

  const renderEmployeeTurnoverRepresentation = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Employee turnover by Gender
      </Typography>
      {/* <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Please indicate the percentage ranges for gender representation in leadership positions.
      </Typography> */}

      <Box sx={{ mb: 4 }}>
        {/* Header Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box sx={{ width: '120px' }}></Box>
          <Box sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              0 - 25%
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              26% - 50%
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              51% - 75%
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              76% - 100%
            </Typography>
          </Box>
        </Box>

        {/* Female Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Female
          </Typography>
          <RadioGroup
            row
            value={formData.femaleEmployeeTurnoverGenderRange}
            onChange={handleInputChange('femaleEmployeeTurnoverGenderRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="51-75"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="76-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>

        {/* Male Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Male
          </Typography>
          <RadioGroup
            row
            value={formData.maleEmployeeTurnoverGenderRange}
            onChange={handleInputChange('maleEmployeeTurnoverGenderRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="51-75"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="76-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>
      </Box>

      <Typography variant="h6" gutterBottom>
        Employee turnover by Age
      </Typography>
      {/* <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Please indicate the percentage ranges for gender representation in leadership positions.
      </Typography> */}

      <Box sx={{ mb: 4 }}>
        {/* Header Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box sx={{ width: '120px' }}></Box>
          <Box sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              Under 30 years
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              30-50 years old
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              Over 50 years old
            </Typography>
          </Box>
        </Box>

        {/* Female Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Female
          </Typography>
          <RadioGroup
            row
            value={formData.femaleEmployeeTurnoverAgeRange}
            onChange={handleInputChange('femaleEmployeeTurnoverAgeRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-30"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="30-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="50-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>

        {/* Male Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Male
          </Typography>
          <RadioGroup
            row
            value={formData.maleEmployeeTurnoverAgeRange}
            onChange={handleInputChange('maleEmployeeTurnoverAgeRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-30"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="30-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="50-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>
      </Box>
      <Typography variant="h6" gutterBottom>
        Employee turnover by  Employee Category
      </Typography>
      {/* <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Please indicate the percentage ranges for gender representation in leadership positions.
      </Typography> */}

      <Box sx={{ mb: 4 }}>
        {/* Header Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box sx={{ width: '120px' }}></Box>
          <Box sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              0 - 25%
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              26% - 50%
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              51% - 75%
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              76% - 100%
            </Typography>
          </Box>
        </Box>

        {/* Leadership Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Leadership
          </Typography>
          <RadioGroup
            row
            value={formData.leadershipEmployeeTurnoverCategoryRange}
            onChange={handleInputChange('leadershipEmployeeTurnoverCategoryRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="51-75"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="76-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>

        {/* Management Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Management
          </Typography>
          <RadioGroup
            row
            value={formData.managementEmployeeTurnoverCategoryRange}
            onChange={handleInputChange('managementEmployeeTurnoverCategoryRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="51-75"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="76-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>

        {/* Technical Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Technical
          </Typography>
          <RadioGroup
            row
            value={formData.technicalEmployeeTurnoverCategoryRange}
            onChange={handleInputChange('technicalEmployeeTurnoverCategoryRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="51-75"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="76-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>

        {/* Other Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Other
          </Typography>
          <RadioGroup
            row
            value={formData.otherEmployeeTurnoverCategoryRange}
            onChange={handleInputChange('otherEmployeeTurnoverCategoryRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="51-75"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="76-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>
      </Box>
    </Box>
  );

  const renderNewEmployeeHiresRepresentation = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        New employee hires by Gender
      </Typography>
      {/* <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Please indicate the percentage ranges for gender representation in leadership positions.
      </Typography> */}

      <Box sx={{ mb: 4 }}>
        {/* Header Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box sx={{ width: '120px' }}></Box>
          <Box sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              0 - 25%
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              26% - 50%
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              51% - 75%
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              76% - 100%
            </Typography>
          </Box>
        </Box>

        {/* Female Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Female
          </Typography>
          <RadioGroup
            row
            value={formData.femaleNewEmployeeHiresGenderRange}
            onChange={handleInputChange('femaleNewEmployeeHiresGenderRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="51-75"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="76-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>

        {/* Male Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Male
          </Typography>
          <RadioGroup
            row
            value={formData.maleNewEmployeeHiresGenderRange}
            onChange={handleInputChange('maleNewEmployeeHiresGenderRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="51-75"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="76-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>
      </Box>

      <Typography variant="h6" gutterBottom>
        New employee hires by Age
      </Typography>
      {/* <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Please indicate the percentage ranges for gender representation in leadership positions.
      </Typography> */}

      <Box sx={{ mb: 4 }}>
        {/* Header Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box sx={{ width: '120px' }}></Box>
          <Box sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              Under 30 years
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              30-50 years old
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              Over 50 years old
            </Typography>
          </Box>
        </Box>

        {/* Female Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Female
          </Typography>
          <RadioGroup
            row
            value={formData.femaleNewEmployeeHiresAgeRange}
            onChange={handleInputChange('femaleNewEmployeeHiresAgeRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-30"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="30-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="50-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>

        {/* Male Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Male
          </Typography>
          <RadioGroup
            row
            value={formData.maleNewEmployeeHiresAgeRange}
            onChange={handleInputChange('maleNewEmployeeHiresAgeRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-30"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="30-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="50-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>
      </Box>
      <Typography variant="h6" gutterBottom>
        New employee hires by Employee Category
      </Typography>
      {/* <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Please indicate the percentage ranges for gender representation in leadership positions.
      </Typography> */}

      <Box sx={{ mb: 4 }}>
        {/* Header Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box sx={{ width: '120px' }}></Box>
          <Box sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              0 - 25%
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              26% - 50%
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              51% - 75%
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              76% - 100%
            </Typography>
          </Box>
        </Box>

        {/* Leadership Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Leadership
          </Typography>
          <RadioGroup
            row
            value={formData.leadershipNewEmployeeHiresCategoryRange}
            onChange={handleInputChange('leadershipNewEmployeeHiresCategoryRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="51-75"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="76-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>

        {/* Management Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Management
          </Typography>
          <RadioGroup
            row
            value={formData.managementNewEmployeeHiresCategoryRange}
            onChange={handleInputChange('managementNewEmployeeHiresCategoryRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="51-75"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="76-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>

        {/* Technical Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Technical
          </Typography>
          <RadioGroup
            row
            value={formData.technicalNewEmployeeHiresCategoryRange}
            onChange={handleInputChange('technicalNewEmployeeHiresCategoryRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="51-75"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="76-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>

        {/* Other Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Other
          </Typography>
          <RadioGroup
            row
            value={formData.otherNewEmployeeHiresCategoryRange}
            onChange={handleInputChange('otherNewEmployeeHiresCategoryRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="51-75"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="76-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>
      </Box>
      <TextField
        fullWidth
        label="Number of workers who are not employees and whose work is controlled by the organization"
        value={formData.purchasedElectricity}
        onChange={handleInputChange('purchasedElectricity')}
        variant="outlined"
        sx={{ flex: '1 1 300px', minWidth: '250px' }}
        required
      />
    </Box>
  );

  const renderLaborRelations = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Labor Relations
      </Typography>

      <Box>
        <FormControl component="fieldset" sx={{ mb: 3 }} required>
          <FormLabel component="legend">Do you have HR policies for your organization?</FormLabel>
          <RadioGroup
            value={formData.haveHRPolicy}
            onChange={handleInputChange('haveHRPolicy')}
          >
            <FormControlLabel value="yes" control={<Radio />} label="Yes" />
            <FormControlLabel value="no" control={<Radio />} label="No" />
          </RadioGroup>
        </FormControl>
      </Box>

      <Box>
        <FormControl component="fieldset" sx={{ mb: 3 }} required>
          <FormLabel component="legend">Do your policies include the following elements? (Select all applicable)
          </FormLabel>
          <RadioGroup
            value={formData.hrPoliciesInclude}
            onChange={handleInputChange('hrPoliciesInclude')}
          >
            <FormControlLabel value="Recruitment practices" control={<Radio />} label="Recruitment practices" />
            <FormControlLabel value="Placement" control={<Radio />} label="Placement" />
            <FormControlLabel value="Benefits" control={<Radio />} label="Benefits (include the definition as info pop-up)" />
            <FormControlLabel value="Min. notice periods" control={<Radio />} label="Min. notice periods (include the definition as info pop-up)" />
            <FormControlLabel value="Non discriminatory" control={<Radio />} label="Non discriminatory" />
            <FormControlLabel value="Other" control={<Radio />} label="Other" />
            <FormControlLabel value="All" control={<Radio />} label="All" />
          </RadioGroup>
        </FormControl>
      </Box>

      <TextField
        fullWidth
        label="Describe how policies are communicated to your stakeholders (employees, business partners, and other relevant parties)"
        value={formData.policiesCommunicated}
        onChange={handleInputChange('policiesCommunicated')}
        variant="outlined"
        sx={{ flex: '1 1 300px', minWidth: '250px' }}
        required
      />
    </Box>
  );

  const renderChildForcedOrCompulsoryLabour = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Child forced or compulsory labour
      </Typography>
      {/* <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        For the period January to December 2023
      </Typography> */}

      <TextField
        fullWidth
        label="Do you have mechanisms for age verification in recruitment procedures? Please describe"
        value={formData.ageVerificationMechanism}
        onChange={handleInputChange('ageVerificationMechanism')}
        required
        variant="outlined"
        sx={{ mb: 3 }}
      />

      <Typography variant="body1" color="text.secondary">
        What is your policy (measures to be taken) towards operations and suppliers considered to have significant risk for incidents of Child labour / Forced labour / Young workers exposed to hazardous work? Please describe
      </Typography>
      <TextField
        fullWidth
        value={formData.childLabourRiskPolicy}
        onChange={handleInputChange('childLabourRiskPolicy')}
        required
        variant="outlined"
        sx={{ mb: 3 }}
      />
    </Box>
  );

  const renderStageOneHealthAndWellbeing = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Health and Safety policies
      </Typography>
      {/* <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        For the period January to December 2023
      </Typography> */}

      <Box>
        <FormControl component="fieldset" sx={{ mb: 3 }} required>
          <FormLabel component="legend">Does your organization have a health and safety policy?</FormLabel>
          <RadioGroup
            value={formData.haveHealthAndSafetyPolicy}
            onChange={handleInputChange('haveHealthAndSafetyPolicy')}
          >
            <FormControlLabel value="yes" control={<Radio />} label="Yes" />
            <FormControlLabel value="no" control={<Radio />} label="No" />
          </RadioGroup>
        </FormControl>
      </Box>

      <Box>
        <FormControl component="fieldset" sx={{ mb: 3 }} required>
          <FormLabel component="legend">Does your policy include the following.</FormLabel>
          <RadioGroup
            value={formData.healthPoliciesInclude}
            onChange={handleInputChange('healthPoliciesInclude')}
          >
            <FormControlLabel value="procedures to identify work-related hazards and assess risks in order to eliminate hazards and minimize risks" control={<Radio />} label="procedures to identify work-related hazards and assess risks in order to eliminate hazards and minimize risks" sx={{ mb: 1 }}/>
            <FormControlLabel value="procedures for employees to report work-related hazards and hazardous situations" control={<Radio />} label="procedures for employees to report work-related hazards and hazardous situations" sx={{ mb: 1 }}/>
            <FormControlLabel value="procedures for employees to remove themselves from work situations that they believe could cause injury or ill health" control={<Radio />} label="procedures for employees to remove themselves from work situations that they believe could cause injury or ill health (include the definition as info pop-up)" sx={{ mb: 1 }}/>
            <FormControlLabel value="procedures to investigate work-related incidents, to determine corrective actions using the hierarchy of controls, and to determine improvements needed in the occupational health and safety management system" control={<Radio />} label="procedures to investigate work-related incidents, to determine corrective actions using the hierarchy of controls, and to determine improvements needed in the occupational health and safety management system (include the definition as info pop-up)" sx={{ mb: 1 }}/>
            <FormControlLabel value="All" control={<Radio />} label="All" sx={{ mb: 1 }}/>
            <FormControlLabel value="Other" control={<Radio />} label="Other" sx={{ mb: 1 }}/>
          </RadioGroup>
        </FormControl>
      </Box>
    </Box>
  );

  const renderStageOneSkillDevelopment = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Skills Development
      </Typography>
      {/* <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        For the period January to December 2023
      </Typography> */}

      <Box>
        <FormControl component="fieldset" sx={{ mb: 3 }} required>
          <FormLabel component="legend">Do you have policies for training and advancement of staff at all levels?</FormLabel>
          <RadioGroup
            value={formData.haveTrainingPolicies}
            onChange={handleInputChange('haveTrainingPolicies')}
          >
            <FormControlLabel value="yes" control={<Radio />} label="Yes" />
            <FormControlLabel value="no" control={<Radio />} label="No" />
          </RadioGroup>
        </FormControl>
      </Box>

      {formData.haveTrainingPolicies === 'yes' && (
        <TextField
          fullWidth
          label="Describe the policies"
          value={formData.describeTrainingPolicy}
          onChange={handleInputChange('describeTrainingPolicy')}
          variant="outlined"
          sx={{ mb: 1 }}
          required
        />
      )}

      <Box>
        <FormControl component="fieldset" sx={{ mb: 3 }} required>
          <FormLabel component="legend">Are your staff aware of skills development training they have access to?</FormLabel>
          <RadioGroup
            value={formData.haveSkillDevelopmentTraining}
            onChange={handleInputChange('haveSkillDevelopmentTraining')}
          >
            <FormControlLabel value="yes" control={<Radio />} label="Yes" />
            <FormControlLabel value="no" control={<Radio />} label="No" />
          </RadioGroup>
        </FormControl>
      </Box>

      {/* <Typography variant="h6" gutterBottom>
        New employee hires by Age
      </Typography> */}
      <Typography variant='body1' color="text.secondary" sx={{ mb: 3 }}>
        Specify the average hours of training that your organization’s employees have undertaken during the reporting period
      </Typography>

      <Box sx={{ mb: 4 }}>
        {/* Header Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box sx={{ width: '120px' }}></Box>
          <Box sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              0 - 50 Hr
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              51 - 100 Hr
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              101 - 200 Hr
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              201 - Above
            </Typography>
          </Box>
        </Box>

        {/* Female Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Female
          </Typography>
          <RadioGroup
            row
            value={formData.femaleTrainingHoursGenderRange}
            onChange={handleInputChange('femaleTrainingHoursGenderRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="51-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="101-200"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="201-above"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>

        {/* Male Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Male
          </Typography>
          <RadioGroup
            row
            value={formData.maleTrainingHoursGenderRange}
            onChange={handleInputChange('maleTrainingHoursGenderRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="51-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="101-200"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="201-above"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>
      </Box>

      {/* <Typography variant="h6" gutterBottom>
        New employee hires by Employee Category
      </Typography> */}
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Specify the average hours of training that your organization’s employees have undertaken during the reporting period, by: Employee Category
      </Typography>

      <Box sx={{ mb: 4 }}>
        {/* Header Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box sx={{ width: '120px' }}></Box>
          <Box sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              0 - 50 Hr
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              51 - 100 Hr
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              101 - 200 Hr
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              201 - Above
            </Typography>
          </Box>
        </Box>

        {/* Leadership Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Leadership
          </Typography>
          <RadioGroup
            row
            value={formData.leadershipTrainingHoursCategoryRange}
            onChange={handleInputChange('leadershipTrainingHoursCategoryRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="51-75"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="76-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>

        {/* Management Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Management
          </Typography>
          <RadioGroup
            row
            value={formData.managementTrainingHoursCategoryRange}
            onChange={handleInputChange('managementTrainingHoursCategoryRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="51-75"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="76-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>

        {/* Technical Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Technical
          </Typography>
          <RadioGroup
            row
            value={formData.technicalTrainingHoursCategoryRange}
            onChange={handleInputChange('technicalTrainingHoursCategoryRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="51-75"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="76-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>

        {/* Other Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Other
          </Typography>
          <RadioGroup
            row
            value={formData.otherTrainingHoursCategoryRange}
            onChange={handleInputChange('otherTrainingHoursCategoryRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="51-75"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="76-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>
      </Box>
    </Box>
  );

  const renderProductsAndServices = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Innovation of better products and services
      </Typography>
      {/* <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        For the period January to December 2023
      </Typography> */}

      <Box>
        <FormControl component="fieldset" sx={{ mb: 3 }} required>
          <FormLabel component="legend">Do you have a policy on Tech for Good?</FormLabel>
          <RadioGroup
            value={formData.haveTechForGoodPolicy}
            onChange={handleInputChange('haveTechForGoodPolicy')}
          >
            <FormControlLabel value="yes" control={<Radio />} label="Yes" />
            <FormControlLabel value="no" control={<Radio />} label="No" />
          </RadioGroup>
        </FormControl>
      </Box>

      {formData.haveTechForGoodPolicy === 'yes' && (
        <TextField
          fullWidth
          label="Describe the policies"
          value={formData.describeTechForGoodPolicy}
          onChange={handleInputChange('describeTechForGoodPolicy')}
          variant="outlined"
          sx={{ mb: 1 }}
          required
        />
      )}

      <TextField
        fullWidth
        label="Provide the total cost incurred during the year in relation to research and development"
        value={formData.describeTechForGoodPolicy}
        onChange={handleInputChange('describeTechForGoodPolicy')}
        variant="outlined"
        sx={{ mb: 1 }}
        required
      />
    </Box>
  );

  const renderCommunityAndSocialVitality = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Community and Social vitality
      </Typography>
      {/* <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        For the period January to December 2023
      </Typography> */}

      <Box>
        <FormControl component="fieldset" sx={{ mb: 3 }} required>
          <FormLabel component="legend">Do you have data security policies?</FormLabel>
          <RadioGroup
            value={formData.haveDataSecurityPolicy}
            onChange={handleInputChange('haveDataSecurityPolicy')}
          >
            <FormControlLabel value="yes" control={<Radio />} label="Yes" />
            <FormControlLabel value="no" control={<Radio />} label="No" />
          </RadioGroup>
        </FormControl>
      </Box>

      {formData.haveDataSecurityPolicy === 'yes' && (
        <TextField
          fullWidth
          label="Describe your data security policies"
          value={formData.describeDataSecurityPolicy}
          onChange={handleInputChange('describeDataSecurityPolicy')}
          variant="outlined"
          sx={{ mb: 3 }}
          required
        />
      )}

      <TextField
        fullWidth
        label="Total number of substantiated complaints received concerning breaches of customer privacy"
        value={formData.substantiatedComplaintsReceived}
        onChange={handleInputChange('substantiatedComplaintsReceived')}
        variant="outlined"
        sx={{ mb: 3 }}
        required
      />

      <TextField
        fullWidth
        label="Total number of identified leaks, thefts, or losses of customer data"
        value={formData.identifiedLossesOfData}
        onChange={handleInputChange('identifiedLossesOfData')}
        variant="outlined"
        sx={{ mb: 3 }}
        required
      />

      <TextField
        fullWidth
        label="Number of unique users who were affected by data breaches"
        value={formData.usersAffectedDataBreaches}
        onChange={handleInputChange('usersAffectedDataBreaches')}
        variant="outlined"
        sx={{ mb: 3 }}
        required
      />
    </Box>
  );

  const renderCommunityEngagement = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Community engagement
      </Typography>
      {/* <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        For the period January to December 2023
      </Typography> */}
      <Box>
        <TextField
          fullWidth
          label="Organization’s engagement with the local communities, if any."
          value={formData.organizationEngagementCommunities}
          onChange={handleInputChange('organizationEngagementCommunities')}
          variant="outlined"
          sx={{ mb: 3 }}
          multiline
          required
        />

        <Typography variant="h6" gutterBottom>
          Supplier screening
        </Typography>

        <FormControl component="fieldset" sx={{ mb: 3 }} required>
          <FormLabel component="legend">Do you have a policy on supplier social assessment?</FormLabel>
          <RadioGroup
            value={formData.haveSupplierSocialPolicy}
            onChange={handleInputChange('haveSupplierSocialPolicy')}
          >
            <FormControlLabel value="yes" control={<Radio />} label="Yes" />
            <FormControlLabel value="no" control={<Radio />} label="No" />
          </RadioGroup>
        </FormControl>
      </Box>

      <Typography variant="body1" color="text.secondary">
        Describe supplier due diligence policies and procedures in place to identify and assess actual and potential adverse impacts associated with operations, products or services.
      </Typography>
      <TextField
        fullWidth
        value={formData.supplierDueDiligencePolicy}
        onChange={handleInputChange('supplierDueDiligencePolicy')}
        variant="outlined"
        sx={{ mb: 3 }}
        multiline
        required
      />
    </Box>
  );

  const renderPayGap = () => (
    <Box>
      <Typography variant="h6" gutterBottom sx={{ mb: 3 }}>
        Gender pay gap
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Provide ratio of the basic salary and remuneration by gender for each employee category - Female
      </Typography>
      {/* <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Please indicate the percentage ranges for gender representation in leadership positions.
      </Typography> */}

      <Box sx={{ mb: 4 }}>
        {/* Header Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box sx={{ width: '120px' }}></Box>
          <Box sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              Less than 8%
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              9% - 15%
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              16% - 25%
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              26% - 35%
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              More than 36%
            </Typography>
          </Box>
        </Box>

        {/* Leadership Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Leadership
          </Typography>
          <RadioGroup
            row
            value={formData.femaleLeadershipBasicSalleryByGenderRange}
            onChange={handleInputChange('femaleLeadershipBasicSalleryByGenderRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-8"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="9-15"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="16-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-35"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="36-more"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>

        {/* Management Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Management
          </Typography>
          <RadioGroup
            row
            value={formData.femaleManagementBasicSalleryByGenderRange}
            onChange={handleInputChange('femaleManagementBasicSalleryByGenderRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-8"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="9-15"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="16-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-35"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="36-more"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>

        {/* Technical Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Technical
          </Typography>
          <RadioGroup
            row
            value={formData.femaleTechnicalBasicSalleryByGenderRange}
            onChange={handleInputChange('femaleTechnicalBasicSalleryByGenderRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-8"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="9-15"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="16-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-35"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="36-more"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>

        {/* Other Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Other
          </Typography>
          <RadioGroup
            row
            value={formData.femaleOtherBasicSalleryByGenderRange}
            onChange={handleInputChange('femaleOtherBasicSalleryByGenderRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-8"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="9-15"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="16-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-35"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="36-more"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>
      </Box>

      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Provide ratio of the basic salary and remuneration by gender for each employee category - Male
      </Typography>
      {/* <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Please indicate the percentage ranges for gender representation in leadership positions.
      </Typography> */}

      <Box sx={{ mb: 4 }}>
        {/* Header Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box sx={{ width: '120px' }}></Box>
          <Box sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              Less than 8%
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              9% - 15%
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              16% - 25%
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              26% - 35%
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              More than 36%
            </Typography>
          </Box>
        </Box>

        {/* Leadership Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Leadership
          </Typography>
          <RadioGroup
            row
            value={formData.maleLeadershipBasicSalleryByGenderRange}
            onChange={handleInputChange('maleLeadershipBasicSalleryByGenderRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-8"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="9-15"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="16-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-35"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="36-more"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>

        {/* Management Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Management
          </Typography>
          <RadioGroup
            row
            value={formData.maleManagementBasicSalleryByGenderRange}
            onChange={handleInputChange('maleManagementBasicSalleryByGenderRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-8"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="9-15"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="16-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-35"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="36-more"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>

        {/* Technical Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Technical
          </Typography>
          <RadioGroup
            row
            value={formData.maleTechnicalBasicSalleryByGenderRange}
            onChange={handleInputChange('maleTechnicalBasicSalleryByGenderRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-8"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="9-15"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="16-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-35"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="36-more"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>

        {/* Other Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Other
          </Typography>
          <RadioGroup
            row
            value={formData.maleOtherBasicSalleryByGenderRange}
            onChange={handleInputChange('maleOtherBasicSalleryByGenderRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-8"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="9-15"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="16-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-35"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="36-more"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>
      </Box>

      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Provide ratio of standard entry level wage by gender compared to local minimum wage - Female
      </Typography>
      {/* <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Please indicate the percentage ranges for gender representation in leadership positions.
      </Typography> */}

      <Box sx={{ mb: 4 }}>
        {/* Header Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box sx={{ width: '120px' }}></Box>
          <Box sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              2.9 & Above
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              3.9 & Above
            </Typography>
          </Box>
        </Box>

        {/* Leadership Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Leadership
          </Typography>
          <RadioGroup
            row
            value={formData.femaleLeadershipMinimumWageByGenderRange}
            onChange={handleInputChange('femaleLeadershipMinimumWageByGenderRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-8"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="9-15"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>

        {/* Management Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Management
          </Typography>
          <RadioGroup
            row
            value={formData.femaleManagementMinimumWageByGenderRange}
            onChange={handleInputChange('femaleManagementMinimumWageByGenderRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-8"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="9-15"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>

        {/* Technical Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Technical
          </Typography>
          <RadioGroup
            row
            value={formData.femaleTechnicalMinimumWageByGenderRange}
            onChange={handleInputChange('femaleTechnicalMinimumWageByGenderRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-8"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="9-15"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>

        {/* Other Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Other
          </Typography>
          <RadioGroup
            row
            value={formData.femaleOtherMinimumWageByGenderRange}
            onChange={handleInputChange('femaleOtherMinimumWageByGenderRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-8"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="9-15"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>
      </Box>

      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Provide ratio of standard entry level wage by gender compared to local minimum wage - Male
      </Typography>
      {/* <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Please indicate the percentage ranges for gender representation in leadership positions.
      </Typography> */}

      <Box sx={{ mb: 4 }}>
        {/* Header Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box sx={{ width: '120px' }}></Box>
          <Box sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              2.9 & Above
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              3.9 & Above
            </Typography>
          </Box>
        </Box>

        {/* Leadership Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Leadership
          </Typography>
          <RadioGroup
            row
            value={formData.maleLeadershipMinimumWageByGenderRange}
            onChange={handleInputChange('maleLeadershipMinimumWageByGenderRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-8"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="9-15"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>

        {/* Management Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Management
          </Typography>
          <RadioGroup
            row
            value={formData.maleManagementMinimumWageByGenderRange}
            onChange={handleInputChange('maleManagementMinimumWageByGenderRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-8"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="9-15"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>

        {/* Technical Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Technical
          </Typography>
          <RadioGroup
            row
            value={formData.maleTechnicalMinimumWageByGenderRange}
            onChange={handleInputChange('maleTechnicalMinimumWageByGenderRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-8"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="9-15"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>

        {/* Other Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Other
          </Typography>
          <RadioGroup
            row
            value={formData.maleOtherMinimumWageByGenderRange}
            onChange={handleInputChange('maleOtherMinimumWageByGenderRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-8"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="9-15"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>
      </Box>
    </Box>
  );

  const renderParentalLeave = () => (
    <Box>
      <TextField
        fullWidth
        label="Report on number of employees that took parental leave"
        value={formData.tookParentalLeave}
        onChange={handleInputChange('tookParentalLeave')}
        required
        variant="outlined"
        sx={{ mb: 3 }}
      />
      <TextField
        fullWidth
        label="Report on number of employees that returned to work after parental leaves ended"
        value={formData.returnParentalLeaveCount}
        onChange={handleInputChange('returnParentalLeaveCount')}
        required
        variant="outlined"
        sx={{ mb: 3 }}
      />
      <TextField
        fullWidth
        label="Report on number of employees that returned to work after parental leaves ended that were still employed 12 months after their return to work"
        value={formData.stillEmployedCount}
        onChange={handleInputChange('stillEmployedCount')}
        required
        variant="outlined"
        sx={{ mb: 3 }}
      />
      <FormControl sx={{ flex: '1 1 500px', minWidth: '250px', width: '100%' }} required>
        <InputLabel>Retention rates of employees that took parental leave</InputLabel>
        <Select
          value={formData.rateOfTookParentalLeave}
          label="Retention rates of employees that took parental leave"
          onChange={handleInputChange('rateOfTookParentalLeave')}
        >
          <MenuItem value="10-20%">50-70%</MenuItem>
          <MenuItem value="20-40%">70-80%</MenuItem>
          <MenuItem value="40-50%">80-90%</MenuItem>
          <MenuItem value="50-60%">90-100%</MenuItem>
        </Select>
      </FormControl>
    </Box>
  );

  const renderIncidentsOfDiscrimination = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Incidents of discrimination and corrective actions taken
      </Typography>
      <TextField
        fullWidth
        label="Report on the total number of incidents of discrimination during the reporting period"
        value={formData.totalIncidentsOfDiscrimination}
        onChange={handleInputChange('totalIncidentsOfDiscrimination')}
        required
        variant="outlined"
        sx={{ mb: 3 }}
      />
      <Typography variant="body1" sx={{ mb: 1 }}>
        Report the status of the incidents and actions taken with reference to the following
      </Typography>
      <TextField
        fullWidth
        label="Number of incidents reviewed by the organization"
        value={formData.reviewedIncidentsCount}
        onChange={handleInputChange('reviewedIncidentsCount')}
        required
        variant="outlined"
        sx={{ mb: 3 }}
      />
      <TextField
        fullWidth
        label="Number of remediation plans being implemented"
        value={formData.remediationPlansBeingImplemented}
        onChange={handleInputChange('remediationPlansBeingImplemented')}
        required
        variant="outlined"
        sx={{ mb: 3 }}
      />
      <TextField
        fullWidth
        label="Number of remediation plans that have been implemented, with results reviewed through routine internal management review processes"
        value={formData.remediationPlansImplemented}
        onChange={handleInputChange('remediationPlansImplemented')}
        required
        variant="outlined"
        sx={{ mb: 3 }}
      />
      <TextField
        fullWidth
        label="Number of incidents no longer subject to action"
        value={formData.incidentsNoLongerSubjectToAction}
        onChange={handleInputChange('incidentsNoLongerSubjectToAction')}
        required
        variant="outlined"
        sx={{ mb: 3 }}
      />
      <FormControl component="fieldset" sx={{ mb: 3 }} required>
        <FormLabel component="legend">Have you conducted staff training on non-discrimination policies and practices?</FormLabel>
        <RadioGroup
          value={formData.staffTrainingConducted}
          onChange={handleInputChange('staffTrainingConducted')}
        >
          <FormControlLabel value="yes" control={<Radio />} label="Yes" />
          <FormControlLabel value="no" control={<Radio />} label="No" />
        </RadioGroup>
      </FormControl>
      <FormControl component="fieldset" sx={{ mb: 3 }} required>
        <FormLabel component="legend">Have you conducted unconscious bias training for your employees?</FormLabel>
        <RadioGroup
          value={formData.unconsciousBiasTrainingConducted}
          onChange={handleInputChange('unconsciousBiasTrainingConducted')}
        >
          <FormControlLabel value="yes" control={<Radio />} label="Yes" />
          <FormControlLabel value="no" control={<Radio />} label="No" />
        </RadioGroup>
      </FormControl>
    </Box>
  );

  const renderStageTwoHealthAndWellbeing = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Health and Safety management system
      </Typography>
      {/* <Typography variant="body1" sx={{ mb: 1 }}>
        Report on the following
      </Typography> */}
      <TextField
        fullWidth
        label="A statement of whether an occupational health and safety management system has been implemented, including whether"
        value={formData.reviewedIncidentsCount}
        onChange={handleInputChange('reviewedIncidentsCount')}
        required
        variant="outlined"
        sx={{ mb: 3 }}
      />
      <TextField
        fullWidth
        label="The system has been implemented because of legal requirements and, if so, a list of the requirements"
        value={formData.remediationPlansBeingImplemented}
        onChange={handleInputChange('remediationPlansBeingImplemented')}
        required
        variant="outlined"
        sx={{ mb: 3 }}
      />
      <TextField
        fullWidth
        label="The system has been implemented based on recognized risk management and/or management system standards/guidelines and, if so, a list of the standards/guidelines"
        value={formData.remediationPlansImplemented}
        onChange={handleInputChange('remediationPlansImplemented')}
        required
        variant="outlined"
        sx={{ mb: 3 }}
      />
      <Typography variant="body1" color='text.secondary' sx={{ mb: 1 }}>
        A description of the scope of workers, activities, and workplaces covered by the occupational health and safety management system, and an explanation of whether and, if so, why any workers, activities, or workplaces are not covered.
      </Typography>
      <TextField
        fullWidth
        value={formData.incidentsNoLongerSubjectToAction}
        onChange={handleInputChange('incidentsNoLongerSubjectToAction')}
        required
        variant="outlined"
        multiline
        sx={{ mb: 3 }}
      />
      <TextField
        fullWidth
        label="Absentee rate of all employees"
        value={formData.remediationPlansImplemented}
        onChange={handleInputChange('remediationPlansImplemented')}
        required
        variant="outlined"
        sx={{ mb: 3 }}
      />
      <TextField
        fullWidth
        label="Number of employees participating in “best practice” health and well-being programmes"
        value={formData.remediationPlansImplemented}
        onChange={handleInputChange('remediationPlansImplemented')}
        required
        variant="outlined"
        sx={{ mb: 3 }}
      />
      <TextField
        fullWidth
        label="Main types of work-related ill-health for all employees"
        value={formData.remediationPlansImplemented}
        onChange={handleInputChange('remediationPlansImplemented')}
        required
        variant="outlined"
        sx={{ mb: 3 }}
      />
      <Typography variant="h6" gutterBottom>
        Employee engagement on occupational health and safety
      </Typography>
      <Typography variant="body1" color='text.secondary' sx={{ mb: 1 }}>
        A description of the processes for worker participation and consultation in the development, implementation, and evaluation of the occupational health and safety management system, and for providing access to and communicating relevant information on occupational health and safety to workers.
      </Typography>
      <TextField
        fullWidth
        value={formData.incidentsNoLongerSubjectToAction}
        onChange={handleInputChange('incidentsNoLongerSubjectToAction')}
        required
        variant="outlined"
        multiline
        sx={{ mb: 3 }}
      />
      <Typography variant="body1" color='text.secondary' sx={{ mb: 1 }}>
        Where formal joint management-worker health and safety committees exist, a description of their responsibilities, meeting frequency, decision-making authority, and whether and, if so, why any workers are not represented by these committees.
      </Typography>
      <TextField
        fullWidth
        value={formData.incidentsNoLongerSubjectToAction}
        onChange={handleInputChange('incidentsNoLongerSubjectToAction')}
        required
        variant="outlined"
        multiline
        sx={{ mb: 3 }}
      />
      <Typography variant="body1" color='text.secondary' sx={{ mb: 1 }}>
        A description of any occupational health and safety training provided to workers, including generic training as well as training on specific work-related hazards, hazardous activities, or hazardous situations.
      </Typography>
      <TextField
        fullWidth
        value={formData.incidentsNoLongerSubjectToAction}
        onChange={handleInputChange('incidentsNoLongerSubjectToAction')}
        required
        variant="outlined"
        multiline
        sx={{ mb: 3 }}
      />
    </Box>
  );

  const renderStageTwoSkillDevelopment = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Employee upskilling and transition assistance
      </Typography>
      {/* <Typography variant="body1" sx={{ mb: 1 }}>
        Report on the following
      </Typography> */}
      <TextField
        fullWidth
        label="Report on the type and scope of programs implemented and assistance provided to upgrade employee skills"
        value={formData.scopeOfProgrammes}
        onChange={handleInputChange('scopeOfProgrammes')}
        required
        variant="outlined"
        sx={{ mb: 3 }}
      />
      <Typography variant="body1" color='text.secondary' sx={{ mb: 1 }}>
        Specify the transition assistance programs provided to facilitate continued employability and the management of career endings resulting from retirement or termination of employment.
      </Typography>
      <TextField
        fullWidth
        value={formData.specifyAssistancePrograms}
        onChange={handleInputChange('specifyAssistancePrograms')}
        required
        variant="outlined"
        multiline
        sx={{ mb: 3 }}
      />
    </Box>
  );

  const renderEmployeeReviews = () => (
    <Box>
      <Typography variant="h6" gutterBottom sx={{ mb: 3 }}>
        Employee performance and career development reviews
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Report on number of employees who received a regular performance and career development review by - Gender
      </Typography>
      {/* <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Please indicate the percentage ranges for gender representation in leadership positions.
      </Typography> */}

      <Box sx={{ mb: 4 }}>
        {/* Header Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box sx={{ width: '120px' }}></Box>
          <Box sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              Under 30 years
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              30-50 years old
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              Over 50 years old
            </Typography>
          </Box>
        </Box>

        {/* Female Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Female
          </Typography>
          <RadioGroup
            row
            value={formData.femaleEmployeeReviewsGenderRange}
            onChange={handleInputChange('femaleEmployeeReviewsGenderRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-30"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="30-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="50-more"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>

        {/* Male Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Male
          </Typography>
          <RadioGroup
            row
            value={formData.maleEmployeeReviewsGenderRange}
            onChange={handleInputChange('maleEmployeeReviewsGenderRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-30"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="30-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="50-more"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>
      </Box>

      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Report on number of employees who received a regular performance and career development review - Female
      </Typography>
      {/* <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Please indicate the percentage ranges for gender representation in leadership positions.
      </Typography> */}

      <Box sx={{ mb: 4 }}>
        {/* Header Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box sx={{ width: '120px' }}></Box>
          <Box sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              0 - 25 %
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              26% - 50 %
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              51 % - 75 %
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              76% - 100 %
            </Typography>
          </Box>
        </Box>

        {/* Leadership Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Leadership
          </Typography>
          <RadioGroup
            row
            value={formData.femaleLeadershipEmployeeReviewsCategoryRange}
            onChange={handleInputChange('femaleLeadershipEmployeeReviewsCategoryRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="51-75"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="76-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>

        {/* Management Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Management
          </Typography>
          <RadioGroup
            row
            value={formData.femaleManagementEmployeeReviewsCategoryRange}
            onChange={handleInputChange('femaleManagementEmployeeReviewsCategoryRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="51-75"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="76-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>

        {/* Technical Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Technical
          </Typography>
          <RadioGroup
            row
            value={formData.femaleTechnicalEmployeeReviewsCategoryRange}
            onChange={handleInputChange('femaleTechnicalEmployeeReviewsCategoryRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="51-75"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="76-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>

        {/* Other Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Other
          </Typography>
          <RadioGroup
            row
            value={formData.femaleOtherEmployeeReviewsCategoryRange}
            onChange={handleInputChange('femaleOtherEmployeeReviewsCategoryRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="51-75"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="76-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>
      </Box>

      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Report on number of employees who received a regular performance and career development review - Male
      </Typography>
      {/* <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Please indicate the percentage ranges for gender representation in leadership positions.
      </Typography> */}

      <Box sx={{ mb: 4 }}>
        {/* Header Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box sx={{ width: '120px' }}></Box>
          <Box sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              0 - 25 %
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              26% - 50 %
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              51 % - 75 %
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'center', minWidth: '80px' }}>
              76% - 100 %
            </Typography>
          </Box>
        </Box>

        {/* Leadership Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Leadership
          </Typography>
          <RadioGroup
            row
            value={formData.maleLeadershipEmployeeReviewsCategoryRange}
            onChange={handleInputChange('maleLeadershipEmployeeReviewsCategoryRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="51-75"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="76-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>

        {/* Management Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Management
          </Typography>
          <RadioGroup
            row
            value={formData.maleManagementEmployeeReviewsCategoryRange}
            onChange={handleInputChange('maleManagementEmployeeReviewsCategoryRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="51-75"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="76-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>

        {/* Technical Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Technical
          </Typography>
          <RadioGroup
            row
            value={formData.maleTechnicalEmployeeReviewsCategoryRange}
            onChange={handleInputChange('maleTechnicalEmployeeReviewsCategoryRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="51-75"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="76-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>

        {/* Other Row */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="body1" sx={{ width: '120px', fontWeight: 'medium' }}>
            Other
          </Typography>
          <RadioGroup
            row
            value={formData.maleOtherEmployeeReviewsCategoryRange}
            onChange={handleInputChange('maleOtherEmployeeReviewsCategoryRange')}
            sx={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}
          >
            <FormControlLabel
              value="0-25"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="26-50"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="51-75"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
            <FormControlLabel
              value="76-100"
              control={<Radio />}
              label=""
              sx={{ margin: 0, justifyContent: 'center', minWidth: '80px' }}
            />
          </RadioGroup>
        </Box>
      </Box>
    </Box>
  );

  const renderProductsAndCommunity = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Innovation of better products and services
      </Typography>
      {/* <Typography variant="body1" sx={{ mb: 1 }}>
        Report on the following
      </Typography> */}
      <TextField
        fullWidth
        label="Provide details on technology products and solutions launched that are equitable, accessible and affordable"
        value={formData.scopeOfProgrammes}
        onChange={handleInputChange('scopeOfProgrammes')}
        required
        variant="outlined"
        sx={{ mb: 3 }}
      />
      <Typography variant="h6" gutterBottom>
        Community and Social vitality
      </Typography>
      {/* <Typography variant="body1" sx={{ mb: 1 }}>
        Report on the following
      </Typography> */}
      <TextField
        fullWidth
        label="Provide a description of the policies in relation to local community engagement, and development programs"
        value={formData.scopeOfProgrammes}
        onChange={handleInputChange('scopeOfProgrammes')}
        required
        variant="outlined"
        sx={{ mb: 3 }}
      />
      <FormControl component="fieldset" sx={{ mb: 3 }} required>
        <FormLabel component="legend">Did your organization implement initiatives for local impact and communities; Educate, Empower, Enrich</FormLabel>
        <FormGroup>
          {['Computer literacy', 'Digital Learning', 'Community knowledge', 'Reading programs', 'Life skills development', 'Self-defense', 'Cyber security awareness', 'Any other'].map((element) => (
            <FormControlLabel
              key={element}
              control={
                <Checkbox
                  checked={formData.nonRenewableEnergyElements.includes(element)}
                  onChange={handleMultiSelectChange('nonRenewableEnergyElements', element)}
                />
              }
              label={element}
            />
          ))}
        </FormGroup>
      </FormControl>
    </Box>
  );

  const renderSupplierDueDiligence = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Supplier due diligence Analysis
      </Typography>
      {/* <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        For the period January to December 2023
      </Typography> */}

      <TextField
        fullWidth
        label="Number of suppliers assessed for social impacts"
        value={formData.numberSuppliersAssessed}
        onChange={handleInputChange('numberSuppliersAssessed')}
        required
        variant="outlined"
        sx={{ mb: 3 }}
      />
      <TextField
        fullWidth
        label="Number of suppliers identified as having significant actual and potential negative social impacts"
        value={formData.numberSuppliersSignificantImpacts}
        onChange={handleInputChange('numberSuppliersSignificantImpacts')}
        required
        variant="outlined"
        sx={{ mb: 3 }}
      />
      <TextField
        fullWidth
        label="Number of suppliers with which improvements were agreed upon as a result of assessment"
        value={formData.numberSuppliersImprovementsAgreed}
        onChange={handleInputChange('numberSuppliersImprovementsAgreed')}
        required
        variant="outlined"
        sx={{ mb: 3 }}
      />
      <TextField
        fullWidth
        label="Number of suppliers with which relationships were terminated as a result of assessment"
        value={formData.numberSuppliersRelationshipTerminated}
        onChange={handleInputChange('numberSuppliersRelationshipTerminated')}
        required
        variant="outlined"
        sx={{ mb: 3 }}
      />
      <TextField
        fullWidth
        label="List significant actual and potential negative social impacts identified in the supply chain"
        value={formData.significantNegativeSocialImpacts}
        onChange={handleInputChange('significantNegativeSocialImpacts')}
        required
        variant="outlined"
        sx={{ mb: 3 }}
      />
    </Box>
  );

  const renderCertificationInformation = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Certifications
      </Typography>
      {/* <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        For the period January to December 2023
      </Typography> */}

      <TextField
        fullWidth
        label="List out all the certifications related to Diversity and Inclusion, that the organization has pursued"
        value={formData.companyName}
        onChange={handleInputChange('companyName')}
        required
        variant="outlined"
        multiline
        sx={{ mb: 3 }}
      />

      <Typography variant="h6" gutterBottom>
        Health and Well-being
      </Typography>
      {/* <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        For the period January to December 2023
      </Typography> */}

      <TextField
        fullWidth
        label="List out all the certifications related to Health and Safety, that the organization has pursued"
        value={formData.companyName}
        onChange={handleInputChange('companyName')}
        required
        variant="outlined"
        multiline
        sx={{ mb: 3 }}
      />
    </Box>
  );

  const renderStageThreeSkillDevelopment = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Monetized impacts of training
      </Typography>
      {/* <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        For the period January to December 2023
      </Typography> */}

      <TextField
        fullWidth
        label="Increased revenue for the organization due to training - Increased by (%)"
        value={formData.companyName}
        onChange={handleInputChange('companyName')}
        required
        variant="outlined"
        multiline
        sx={{ mb: 3 }}
      />
      <TextField
        fullWidth
        label="Productivity gains due to training - Increased by (%)"
        value={formData.companyName}
        onChange={handleInputChange('companyName')}
        required
        variant="outlined"
        multiline
        sx={{ mb: 3 }}
      />
      <TextField
        fullWidth
        label="Employee engagement due to training – Increased"
        value={formData.companyName}
        onChange={handleInputChange('companyName')}
        required
        variant="outlined"
        multiline
        sx={{ mb: 3 }}
      />
      <TextField
        fullWidth
        label="Internal hire rates – Increased"
        value={formData.companyName}
        onChange={handleInputChange('companyName')}
        required
        variant="outlined"
        multiline
        sx={{ mb: 3 }}
      />

      <Typography variant="h6" gutterBottom>
        Innovation of better products and services
      </Typography>
      {/* <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        For the period January to December 2023
      </Typography> */}

      <TextField
        fullWidth
        label="Benefits on revenue from services designed to deliver specific social"
        value={formData.companyName}
        onChange={handleInputChange('companyName')}
        required
        variant="outlined"
        multiline
        sx={{ mb: 3 }}
      />
      <TextField
        fullWidth
        label="List all Tech for good recognitions / certifications obtained by the organization"
        value={formData.companyName}
        onChange={handleInputChange('companyName')}
        required
        variant="outlined"
        multiline
        sx={{ mb: 3 }}
      />
    </Box>
  );

  const renderStageThreeCommunityAndSocialVitality = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Local community engagement – Impact Assessments
      </Typography>
      {/* <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        For the period January to December 2023
      </Typography> */}

      <TextField
        fullWidth
        label="Report on the, Social impact assessments, including gender impact assessments, based on participatory processes"
        value={formData.companyName}
        onChange={handleInputChange('companyName')}
        required
        variant="outlined"
        multiline
        sx={{ mb: 3 }}
      />
      <TextField
        fullWidth
        label="Report on the, Public disclosure of results of social impact assessments"
        value={formData.companyName}
        onChange={handleInputChange('companyName')}
        required
        variant="outlined"
        multiline
        sx={{ mb: 3 }}
      />
      <TextField
        fullWidth
        label="Report on the, Works councils, occupational health and safety committees and other worker representation bodies to deal with impacts"
        value={formData.companyName}
        onChange={handleInputChange('companyName')}
        required
        variant="outlined"
        multiline
        sx={{ mb: 3 }}
      />
      <TextField
        fullWidth
        label="Report on the, Local community development programs based on local communities’ needs"
        value={formData.companyName}
        onChange={handleInputChange('companyName')}
        required
        variant="outlined"
        multiline
        sx={{ mb: 3 }}
      />
      <TextField
        fullWidth
        label="Report on the, Stakeholder engagement plans based on stakeholder mapping; broad based local community consultation committees and processes that include vulnerable groups"
        value={formData.companyName}
        onChange={handleInputChange('companyName')}
        required
        variant="outlined"
        multiline
        sx={{ mb: 3 }}
      />
      <TextField
        fullWidth
        label="Report on the, Formal local community grievance processes"
        value={formData.companyName}
        onChange={handleInputChange('companyName')}
        required
        variant="outlined"
        multiline
        sx={{ mb: 3 }}
      />
    </Box>
  );

  const renderCurrentSection = () => {
    switch (currentSection) {
      case 0:
        return renderGenderRepresentation();
      case 1:
        return renderAgeRepresentation();
      case 2:
        return renderDisabilityRepresentation();
      case 3:
        return renderEthnicityRepresentation();
      case 4:
        return renderIndividualsRepresentation();
      case 5:
        return renderEmployeeTurnoverRepresentation();
      case 6:
        return renderNewEmployeeHiresRepresentation();
      case 7:
        return renderLaborRelations();
      case 8:
        return renderChildForcedOrCompulsoryLabour();
      case 9:
        return renderStageOneHealthAndWellbeing();
      case 10:
        return renderStageOneSkillDevelopment();
      case 11:
        return renderProductsAndServices();
      case 12:
        return renderCommunityAndSocialVitality();
      case 13:
        return renderCommunityEngagement();
      case 14:
        return renderPayGap();
      case 15:
        return renderParentalLeave();
      case 16:
        return renderIncidentsOfDiscrimination();
      case 17:
        return renderStageTwoHealthAndWellbeing();
      case 18:
        return renderStageTwoSkillDevelopment();
      case 19:
        return renderEmployeeReviews();
      case 20:
        return renderProductsAndCommunity();
      case 21:
        return renderSupplierDueDiligence();
      case 22:
        return renderCertificationInformation();
      case 23:
        return renderStageThreeSkillDevelopment();
      case 24:
        return renderStageThreeCommunityAndSocialVitality();
      default:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              {sections[currentSection]}
            </Typography>
            <Typography variant="body1" color="text.secondary">
              This section is under development. Please navigate to other sections.
            </Typography>
          </Box>
        );
    }
  };

  return (
    <ThemeProvider theme={theme}>
      <Box>
        {/* Header Bar */}
        <AppBar position="fixed" sx={{ bgcolor: '#CFAB8D', mb: 4 }}>
          <Toolbar sx={{
            display: 'flex',
            alignItems: 'center',
            flexDirection: { xs: 'column', sm: 'row' },
            py: { xs: 0.5, sm: 1 },
            px: { xs: 1, sm: 3 },
            gap: { xs: 0.5, sm: 0 },
            minHeight: { xs: 'auto', sm: '64px' }
          }}>
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              order: { xs: 1, sm: 1 }
            }}>
              <Box
                component="img"
                src={logo}
                alt="SLASSCOM Logo"
                sx={{
                  height: { xs: '24px', sm: '40px' },
                  width: 'auto'
                }}
              />
            </Box>
            <Typography
              component="div"
              sx={{
                flexGrow: 1,
                fontWeight: '600',
                textAlign: 'center',
                fontSize: { xs: '0.75rem', sm: '1rem', md: '1.2rem', lg: '1.5rem' },
                order: { xs: 3, sm: 2 },
                px: { xs: 0.5, sm: 2 },
                py: { xs: 0.5, sm: 0 },
                lineHeight: { xs: 1.2, sm: 1.5 }
              }}
            >
              Environment, Social & Governance (ESG) - Baseline Standards - Social Factors
            </Typography>
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              order: { xs: 2, sm: 3 }
            }}>
              <Box
                component="img"
                src={ebiLogo}
                alt="EBI Logo"
                sx={{
                  height: { xs: '32px', sm: '60px' },
                  width: 'auto'
                }}
              />
            </Box>
          </Toolbar>
        </AppBar>

        <Box sx={{
          display: 'flex',
          justifyContent: 'center',
          py: { xs: 2, sm: 4 },
          px: { xs: 1, sm: 2 }
        }}>
          <Container maxWidth="lg" sx={{ width: '100%' }}>
            <Paper elevation={3} sx={{
              p: { xs: 2, sm: 3, md: 4 },
              bgcolor: '#ECEEDF',
              mt: { xs: 15, sm: 2 },
              mx: { xs: 0, sm: 'auto' }
            }}>
              {/* Progress Indicator */}
              <Box sx={{ mb: { xs: 3, sm: 4 } }}>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  align="center"
                  sx={{
                    mb: 1,
                    fontSize: { xs: '0.75rem', sm: '0.875rem' }
                  }}
                >
                  Section {currentSection + 1} of {sections.length}: {sections[currentSection]}
                </Typography>
                <Box sx={{ width: '100%', bgcolor: 'grey.300', borderRadius: 1, height: { xs: 6, sm: 8 } }}>
                  <Box
                    sx={{
                      width: `${((currentSection + 1) / sections.length) * 100}%`,
                      bgcolor: 'green',
                      height: { xs: 6, sm: 8 },
                      borderRadius: 1,
                      transition: 'width 0.3s ease'
                    }}
                  />
                </Box>
              </Box>

              <Divider sx={{ my: 3 }} />

              <Box component="form" onSubmit={handleSubmit} noValidate>
                {renderCurrentSection()}
              </Box>

              {/* Navigation Buttons */}
              <Box sx={{
                display: 'flex',
                justifyContent: 'space-between',
                mt: { xs: 3, sm: 4 },
                flexDirection: { xs: 'column', sm: 'row' },
                gap: { xs: 2, sm: 0 }
              }}>
                <Button
                  variant="outlined"
                  onClick={handlePrevious}
                  disabled={currentSection === 0}
                  color='success'
                  sx={{
                    order: { xs: 2, sm: 1 },
                    alignSelf: { xs: 'stretch', sm: 'auto' }
                  }}
                >
                  <ArrowBack sx={{ mr: { xs: 1, sm: 0 } }} />
                  <Box sx={{ display: { xs: 'inline', sm: 'none' } }}>Previous</Box>
                </Button>

                <Box sx={{
                  display: 'flex',
                  gap: { xs: 1, sm: 2 },
                  order: { xs: 1, sm: 2 },
                  flexDirection: { xs: 'column', sm: 'row' }
                }}>
                  {currentSection === sections.length - 1 ? (
                    <Button
                      variant="contained"
                      onClick={handleSubmit}
                      sx={{
                        minWidth: { xs: 'auto', sm: 120 },
                        flex: { xs: 1, sm: 'none' }
                      }}
                      color='success'
                    >
                      Submit Form
                    </Button>
                  ) : (
                    <Button
                      variant="contained"
                      onClick={handleNext}
                      color='success'
                      sx={{
                        flex: { xs: 1, sm: 'none' }
                      }}
                    >
                      <Box sx={{ display: { xs: 'inline', sm: 'none' }, mr: 1 }}>Next</Box>
                      <ArrowForward />
                    </Button>
                  )}

                  <Button
                    variant="outlined"
                    onClick={handleClearFormClick}
                    sx={{
                      minWidth: { xs: 'auto', sm: 120 },
                      flex: { xs: 1, sm: 'none' }
                    }}
                    color='success'
                  >
                    Clear Form
                  </Button>
                </Box>
              </Box>
            </Paper>
          </Container>
        </Box>

        {/* Clear Form Confirmation Dialog */}
        <Dialog
          open={clearDialogOpen}
          onClose={handleClearDialogClose}
          aria-labelledby="clear-form-dialog-title"
          aria-describedby="clear-form-dialog-description"
          maxWidth="sm"
          fullWidth
          sx={{
            '& .MuiDialog-paper': {
              mx: { xs: 2, sm: 3 },
              my: { xs: 2, sm: 4 }
            }
          }}
        >
          <DialogTitle
            id="clear-form-dialog-title"
            sx={{
              fontWeight: '600',
              fontSize: { xs: 20, sm: 24 },
              textAlign: 'center',
              px: { xs: 2, sm: 3 },
              py: { xs: 2, sm: 3 }
            }}
          >
            Clear form?
          </DialogTitle>
          <DialogContent sx={{ px: { xs: 2, sm: 3 } }}>
            <DialogContentText
              id="clear-form-dialog-description"
              sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}
            >
              This will remove your answers from all questions, and cannot be undone.
            </DialogContentText>
          </DialogContent>
          <DialogActions sx={{
            p: { xs: 2, sm: 3 },
            pt: 1,
            flexDirection: { xs: 'column', sm: 'row' },
            gap: { xs: 1, sm: 0 }
          }}>
            <Button
              onClick={handleClearDialogClose}
              variant="text"
              sx={{
                color: 'gray',
                fontWeight: '600',
                width: { xs: '100%', sm: 'auto' }
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleConfirmClearForm}
              variant="text"
              sx={{
                color: 'gray',
                fontWeight: '600',
                width: { xs: '100%', sm: 'auto' }
              }}
              autoFocus
            >
              Clear form
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </ThemeProvider>
  );
}

export default App;